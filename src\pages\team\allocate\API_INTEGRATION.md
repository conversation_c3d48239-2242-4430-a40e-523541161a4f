# 团队算力分配页面 API 集成说明

## 集成的 API 接口

### 1. 获取团队算力信息
```bash
curl -X 'GET' \
  'http://***********:8000/api/v1/compute-power/account/team?cid=1&user_id=1' \
  -H 'accept: application/json'
```

### 2. 分配团队算力
```bash
curl -X 'POST' \
  'http://***********:8000/api/v1/compute-power/allocate' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "to_user_id": 0,
  "amount": 0,
  "remark": ""
}'
```

## 服务层实现

`src/service/team.js` 只保留两个必要接口：

```javascript
const teamService = {
  // 获取团队算力信息
  getTeamComputePower(params = {}) {
    return http.get('/api/v1/compute-power/account/team', params)
  },

  // 分配团队算力
  allocateComputePower(data) {
    return http.post('/api/v1/compute-power/allocate', data)
  }
}
```

## 页面功能

1. **页面加载时**: 自动获取团队算力信息
2. **分配算力**: 点击确定按钮时调用分配接口
3. **数据刷新**: 分配成功后重新获取最新数据
4. **错误处理**: 完整的错误提示和用户反馈

## 核心实现

### 获取算力数据
```javascript
const fetchTeamComputePower = async () => {
  const cid = getCid()
  const userId = getUserId()
  const response = await teamService.getTeamComputePower({
    cid: parseInt(cid),
    user_id: parseInt(userId)
  })
}
```

### 分配算力
```javascript
const handleConfirmPower = async () => {
  const response = await teamService.allocateComputePower({
    to_user_id: parseInt(selectedMember.value.id),
    amount: powerValue,
    remark: `分配算力给${selectedMember.value.name}`
  })
}
```
