<template>
  <view class="power-details-page">
    <!-- 顶部导航栏 -->
    <view class="power-details-header">
      <view class="power-details-navbar">
        <view class="power-details-navbar-left" @click="handleBack">
          <image class="power-details-navbar-back" src="@/asset/img/team/icon_back.png" />
        </view>
        <text class="power-details-navbar-title">算力明细</text>
        <view class="power-details-navbar-right" @click="handleShowConsumptionModal">
          <text class="power-details-navbar-consumption">累计消耗</text>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <scroll-view class="power-details-content" scroll-y="true">
      <!-- 算力概览卡片 -->
      <view class="power-details-overview">
        <view class="power-details-overview-left" @click="handleTeamRemainingPower">
          <text class="power-details-overview-value">{{ teamRemainingPower }}</text>
          <view class="power-details-overview-label">
            <text class="power-details-overview-text">团队剩余算力</text>
            <image class="power-details-overview-arrow" src="@/asset/img/team/icon_arrow_right.png" />
          </view>
        </view>
        <view class="power-details-overview-right">
          <text class="power-details-overview-value">{{ teamAvailablePower }}</text>
          <text class="power-details-overview-text">团队可用算力</text>
        </view>
      </view>

      <!-- 标签页导航 -->
      <view class="power-details-tabs">
        <view 
          class="power-details-tab-item"
          :class="{ 'power-details-tab-active': activeTab === 'consumption' }"
          @click="handleTabChange('consumption')"
        >
          <text class="power-details-tab-text">消耗明细</text>
          <view v-if="activeTab === 'consumption'" class="power-details-tab-indicator"></view>
        </view>
        <view 
          class="power-details-tab-item"
          :class="{ 'power-details-tab-active': activeTab === 'allocation' }"
          @click="handleTabChange('allocation')"
        >
          <text class="power-details-tab-text">分配明细</text>
          <view v-if="activeTab === 'allocation'" class="power-details-tab-indicator"></view>
        </view>
      </view>

      <!-- 内容展示区域 -->
      <view class="power-details-content-area">
        <!-- 消耗明细内容 -->
        <view v-if="activeTab === 'consumption'" class="power-details-consumption">
          <view v-if="consumptionList.length === 0" class="power-details-empty">
            <view class="power-details-empty-icon">
              <view class="power-details-empty-folder power-details-empty-folder-back"></view>
              <view class="power-details-empty-folder power-details-empty-folder-front"></view>
              <view class="power-details-empty-sparkle power-details-empty-sparkle-1"></view>
              <view class="power-details-empty-sparkle power-details-empty-sparkle-2"></view>
              <view class="power-details-empty-sparkle power-details-empty-sparkle-3"></view>
            </view>
            <text class="power-details-empty-text">暂无消耗明细</text>
            <text class="power-details-empty-emoji">🙃</text>
          </view>
          <view v-else class="power-details-list">
            <!-- 消耗明细列表 -->
            <view 
              v-for="item in consumptionList" 
              :key="item.id"
              class="power-details-list-item"
            >
              <view class="power-details-list-info">
                <text class="power-details-list-title">{{ item.title }}</text>
                <text class="power-details-list-time">{{ item.time }}</text>
              </view>
              <text class="power-details-list-power">-{{ item.power }}</text>
            </view>
          </view>
        </view>

        <!-- 分配明细内容 -->
        <view v-if="activeTab === 'allocation'" class="power-details-allocation">
          <view v-if="allocationList.length === 0" class="power-details-empty">
            <view class="power-details-empty-icon">
              <view class="power-details-empty-folder power-details-empty-folder-back"></view>
              <view class="power-details-empty-folder power-details-empty-folder-front"></view>
              <view class="power-details-empty-sparkle power-details-empty-sparkle-1"></view>
              <view class="power-details-empty-sparkle power-details-empty-sparkle-2"></view>
              <view class="power-details-empty-sparkle power-details-empty-sparkle-3"></view>
            </view>
            <text class="power-details-empty-text">暂无分配明细</text>
            <text class="power-details-empty-emoji">🙃</text>
          </view>
          <view v-else class="power-details-list">
            <!-- 分配明细列表 -->
            <view 
              v-for="item in allocationList" 
              :key="item.id"
              class="power-details-list-item"
            >
              <view class="power-details-list-info">
                <text class="power-details-list-title">{{ item.title }}</text>
                <text class="power-details-list-time">{{ item.time }}</text>
              </view>
              <text class="power-details-list-power">+{{ item.power }}</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 累计消耗弹窗 -->
    <view v-if="showConsumptionModal" class="power-details-modal">
      <view class="power-details-overlay" @click="handleCloseModal"></view>
      <view class="power-details-modal-content">
        <text class="power-details-modal-title">团队累计消耗算力</text>
        <text class="power-details-modal-content-text">当前已累计消耗 {{ totalConsumption }} 算力</text>
        <view class="power-details-modal-button" @click="handleCloseModal">
          <text class="power-details-modal-button-text">我知道了</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const activeTab = ref('consumption')
const showConsumptionModal = ref(false)

// 算力数据
const teamRemainingPower = ref(0)
const teamAvailablePower = ref(0)
const totalConsumption = ref(0)

// 消耗明细列表
const consumptionList = ref([])

// 分配明细列表
const allocationList = ref([])

// 返回上一页
const handleBack = () => {
  uni.navigateBack()
}

// 显示累计消耗弹窗
const handleShowConsumptionModal = () => {
  showConsumptionModal.value = true
}

// 关闭弹窗
const handleCloseModal = () => {
  showConsumptionModal.value = false
}

// 切换标签页
const handleTabChange = (tab) => {
  activeTab.value = tab
}

// 团队剩余算力点击
const handleTeamRemainingPower = () => {
  uni.showToast({
    title: '团队剩余算力详情',
    icon: 'none'
  })
}

// 初始化数据
const initData = () => {
  // 这里可以从API获取数据
  teamRemainingPower.value = 0
  teamAvailablePower.value = 0
  totalConsumption.value = 0
  consumptionList.value = []
  allocationList.value = []
}

// 页面加载时初始化
onMounted(() => {
  initData()
})
</script>

<style lang="scss" scoped>
.power-details-page {
  min-height: 100vh;
  background-color: #fff;
  position: relative;
}

// 顶部导航栏
.power-details-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #fff;
  padding-top: var(--status-bar-height);
}

.power-details-navbar {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 24rpx;
  background-color: #fff;
}

.power-details-navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.power-details-navbar-back {
  width: 40rpx;
  height: 40rpx;
}

.power-details-navbar-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #000;
  flex: 1;
  text-align: center;
}

.power-details-navbar-right {
  width: 120rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.power-details-navbar-consumption {
  font-size: 28rpx;
  color: #000;
}

// 主要内容区域
.power-details-content {
  margin-top: calc(var(--status-bar-height) + 88rpx);
  padding: 24rpx;
  height: calc(100vh - var(--status-bar-height) - 88rpx);
  background-color: #fff;
}

// 算力概览卡片
.power-details-overview {
  display: flex;
  flex-direction: row;
  background-color: #f8f8f8;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  margin-bottom: 32rpx;
}

.power-details-overview-left {
  display: flex;
  flex-direction: column;
  flex: 1;
  align-items: center;
}

.power-details-overview-right {
  display: flex;
  flex-direction: column;
  flex: 1;
  align-items: center;
}

.power-details-overview-value {
  font-size: 48rpx;
  font-weight: 500;
  color: #000;
  margin-bottom: 8rpx;
}

.power-details-overview-label {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.power-details-overview-text {
  font-size: 26rpx;
  color: #666;
  margin-right: 8rpx;
}

.power-details-overview-arrow {
  width: 20rpx;
  height: 20rpx;
}

// 标签页导航
.power-details-tabs {
  display: flex;
  flex-direction: row;
  margin-bottom: 24rpx;
}

.power-details-tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
  padding: 16rpx 0;
}

.power-details-tab-text {
  font-size: 28rpx;
  color: #999;
  font-weight: 400;
}

.power-details-tab-active .power-details-tab-text {
  color: #000;
  font-weight: 500;
}

.power-details-tab-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #4CAF50;
  border-radius: 2rpx;
}

// 内容展示区域
.power-details-content-area {
  background-color: #fff;
  border-radius: 16rpx;
  min-height: 400rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 空状态
.power-details-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.power-details-empty-icon {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 32rpx;
}

.power-details-empty-folder {
  position: absolute;
  width: 80rpx;
  height: 60rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
}

.power-details-empty-folder-back {
  top: 20rpx;
  left: 20rpx;
  transform: rotate(-5deg);
}

.power-details-empty-folder-front {
  top: 10rpx;
  left: 30rpx;
  transform: rotate(5deg);
}

.power-details-empty-sparkle {
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 50%;
}

.power-details-empty-sparkle-1 {
  top: 0;
  right: 20rpx;
}

.power-details-empty-sparkle-2 {
  top: 40rpx;
  right: 0;
}

.power-details-empty-sparkle-3 {
  bottom: 20rpx;
  right: 40rpx;
}

.power-details-empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.power-details-empty-emoji {
  font-size: 32rpx;
}

// 列表样式
.power-details-list {
  width: 100%;
  padding: 24rpx;
}

.power-details-list-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.power-details-list-item:last-child {
  border-bottom: none;
}

.power-details-list-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.power-details-list-title {
  font-size: 28rpx;
  color: #000;
  font-weight: 400;
  margin-bottom: 8rpx;
}

.power-details-list-time {
  font-size: 24rpx;
  color: #999;
}

.power-details-list-power {
  font-size: 28rpx;
  color: #ff6b6b;
  font-weight: 500;
}

// 弹窗样式
.power-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 200;
  display: flex;
  align-items: center;
  justify-content: center;
}

.power-details-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.power-details-modal-content {
  position: relative;
  width: 600rpx;
  background-color: #fff;
  border-radius: 24rpx;
  padding: 48rpx;
  margin: 0 48rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.power-details-modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #000;
  margin-bottom: 24rpx;
  text-align: center;
}

.power-details-modal-content-text {
  font-size: 28rpx;
  color: #000;
  margin-bottom: 48rpx;
  text-align: center;
}

.power-details-modal-button {
  width: 100%;
  height: 88rpx;
  background-color: #000;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.power-details-modal-button-text {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}
</style>