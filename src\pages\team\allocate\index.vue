<template>
  <view class="allocate-page">
    <!-- 顶部导航栏 -->
    <view class="allocate-header">
      <view class="allocate-navbar">
        <view class="allocate-navbar-left" @click="handleBack">
          <image class="allocate-navbar-back" src="@/asset/img/team/icon_back.png" />
        </view>
        <text class="allocate-navbar-title">分配团队算力</text>
        <view class="allocate-navbar-right" @click="handlePowerDetails">
          <text class="allocate-navbar-details">算力明细</text>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <scroll-view class="allocate-content" scroll-y="true">
        <!-- 创建者区域 -->
        <view class="allocate-creator-section">
          <text class="allocate-section-title">创建者</text>
          <view class="allocate-creator-item">
            <view class="allocate-creator-avatar">
              <image class="allocate-creator-avatar-icon" src="@/asset/img/team/icon_lightning_green.svg" />
            </view>
            <view class="allocate-creator-info">
              <text class="allocate-creator-name">{{ creator.name }}</text>
              <view class="allocate-creator-power">
                <image class="allocate-creator-power-icon" src="@/asset/img/team/icon_lightning_green.svg" />
                <text class="allocate-creator-power-value">{{ creator.power }}</text>
              </view>
            </view>
          </view>
        </view>

      <!-- 成员区域 -->
      <view class="allocate-members-section">
        <text class="allocate-section-title">成员</text>
        <view 
          v-for="member in members" 
          :key="member.id"
          class="allocate-member-item"
        >
                    <view class="allocate-member-avatar">
            <view class="allocate-member-avatar-bg">
              <text class="allocate-member-avatar-text">{{ member.name.charAt(0) }}</text>
            </view>
          </view>
          <view class="allocate-member-info">
            <view class="allocate-member-name-row">
              <text class="allocate-member-name">{{ member.name }}</text>
              <image class="allocate-member-arrow" src="@/asset/img/team/icon_arrow_right.png" />
            </view>
            <view class="allocate-member-power">
              <image class="allocate-member-power-icon" src="@/asset/img/team/icon_lightning_green.svg" />
              <text class="allocate-member-power-value">{{ member.power }}</text>
            </view>
          </view>
          <view class="allocate-member-controls">
            <view class="allocate-member-minus" @click="handleDecreasePower(member)">
              <image class="allocate-member-minus-icon" src="@/asset/img/team/icon_minus.svg" />
            </view>
            <view class="allocate-member-plus" @click="handleIncreasePower(member)">
              <image class="allocate-member-plus-icon" src="@/asset/img/team/icon_plus.svg" />
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 算力增加弹窗 -->
    <view v-if="showPowerModal" class="allocate-power-modal">
      <view class="allocate-power-overlay" @click="closePowerModal"></view>
      <view class="allocate-power-content">
        <!-- 弹窗头部 -->
        <view class="allocate-power-header">
          <view class="allocate-power-close" @click="closePowerModal">
            <image class="allocate-power-close-icon" src="@/asset/img/team/icon_close.png" />
          </view>
          <text class="allocate-power-title">增加当前成员算力</text>
          <view class="allocate-power-placeholder"></view>
        </view>

        <!-- 算力信息 -->
        <view class="allocate-power-info">
          <view class="allocate-power-info-item">
            <text class="allocate-power-info-value">{{ teamAvailablePower }}</text>
            <text class="allocate-power-info-label">团队可用算力</text>
          </view>
          <view class="allocate-power-info-item">
            <text class="allocate-power-info-value">{{ memberAvailablePower }}</text>
            <text class="allocate-power-info-label">成员可用算力</text>
          </view>
        </view>

        <!-- 输入框 -->
        <view class="allocate-power-input">
          <input 
            class="allocate-power-input-field"
            type="number"
            v-model="powerInputValue"
            placeholder="请输入算力数值"
            @input="handlePowerInput"
          />
        </view>

        <!-- 确定按钮 -->
        <view class="allocate-power-confirm" @click="handleConfirmPower">
          <text class="allocate-power-confirm-text">确定增加</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { teamService } from '@/service'
import { getCid, getUserId } from '@/utils/http'

// 响应式数据
const showPowerModal = ref(false)
const powerInputValue = ref('')
const selectedMember = ref(null)
const loading = ref(false)

// 团队算力数据
const teamComputePowerData = ref(null)

// 创建者数据
const creator = ref({
  id: '3253003',
  name: '用户3253003',
  power: 0
})

// 成员数据
const members = ref([
  {
    id: 'member1',
    name: '不门啊',
    avatar: null,
    power: 0
  }
])

// 计算属性
const teamAvailablePower = computed(() => {
  if (teamComputePowerData.value && teamComputePowerData.value.available_power !== undefined) {
    return teamComputePowerData.value.available_power
  }
  return 0 // 默认团队可用算力
})

const memberAvailablePower = computed(() => {
  if (!selectedMember.value) return 0
  return selectedMember.value.power
})

// 返回上一页
const handleBack = () => {
  uni.navigateBack()
}

// 算力明细
const handlePowerDetails = () => {
  uni.navigateTo({
    url: '/pages/team/power-details/index'
  })
}

// 获取团队算力信息
const fetchTeamComputePower = async () => {
  try {
    loading.value = true
    const cid = getCid()
    const userId = getUserId()

    if (!cid || !userId) {
      uni.showToast({
        title: '缺少必要参数',
        icon: 'error'
      })
      return
    }

    const response = await teamService.getTeamComputePower({
      cid: parseInt(cid),
      user_id: parseInt(userId)
    })

    console.log('团队算力信息:', response)

    if (response && response.status_code === 1) {
      teamComputePowerData.value = response.data

      // 更新创建者算力显示（如果API返回了相关数据）
      if (response.data.creator_power !== undefined) {
        creator.value.power = response.data.creator_power
      }

      // 更新成员算力显示（如果API返回了相关数据）
      if (response.data.members && Array.isArray(response.data.members)) {
        members.value = response.data.members.map(member => ({
          id: member.id || member.user_id,
          name: member.name || member.username || member.nickname,
          avatar: member.avatar,
          power: member.power || 0
        }))
      }
    } else {
      uni.showToast({
        title: response.message || '获取算力信息失败',
        icon: 'error'
      })
    }
  } catch (error) {
    console.error('获取团队算力信息失败:', error)
    uni.showToast({
      title: '获取算力信息失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 成员详情
const handleMemberDetail = (member) => {
  uni.showToast({
    title: `${member.name}的详细信息`,
    icon: 'none'
  })
}

// 减少算力
const handleDecreasePower = (member) => {
  if (member.power > 0) {
    member.power--
    uni.showToast({
      title: '算力已减少',
      icon: 'success'
    })
  } else {
    uni.showToast({
      title: '算力已为0',
      icon: 'none'
    })
  }
}

// 增加算力
const handleIncreasePower = (member) => {
  selectedMember.value = member
  showPowerModal.value = true
  powerInputValue.value = ''
}

// 关闭算力弹窗
const closePowerModal = () => {
  showPowerModal.value = false
  selectedMember.value = null
  powerInputValue.value = ''
}

// 算力输入处理
const handlePowerInput = (e) => {
  powerInputValue.value = e.detail.value
}

// 确认增加算力
const handleConfirmPower = async () => {
  const powerValue = parseInt(powerInputValue.value)

  if (isNaN(powerValue) || powerValue <= 0) {
    uni.showToast({
      title: '请输入有效数值',
      icon: 'error'
    })
    return
  }

  if (powerValue > teamAvailablePower.value) {
    uni.showToast({
      title: '算力不足',
      icon: 'error'
    })
    return
  }

  if (!selectedMember.value) {
    uni.showToast({
      title: '请选择成员',
      icon: 'error'
    })
    return
  }

  try {
    uni.showLoading({
      title: '分配中...'
    })

    // 调用分配算力接口
    const response = await teamService.allocateComputePower({
      to_user_id: parseInt(selectedMember.value.id),
      amount: powerValue,
      remark: `分配算力给${selectedMember.value.name}`
    })

    if (response && response.status_code === 1) {
      // 分配成功，更新本地数据
      selectedMember.value.power += powerValue

      uni.showToast({
        title: '算力分配成功',
        icon: 'success'
      })

      // 重新获取最新的算力数据
      await fetchTeamComputePower()
    } else {
      uni.showToast({
        title: response.message || '分配失败',
        icon: 'error'
      })
    }
  } catch (error) {
    console.error('分配算力失败:', error)
    uni.showToast({
      title: '分配失败，请重试',
      icon: 'error'
    })
  } finally {
    uni.hideLoading()
    closePowerModal()
  }
}

// 页面加载时获取团队算力信息
onMounted(() => {
  fetchTeamComputePower()
})
</script>

<style lang="scss" scoped>
.allocate-page {
  min-height: 100vh;
  background-color: #fff;
  position: relative;
}

// 顶部导航栏
.allocate-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #fff;
  padding-top: var(--status-bar-height);
}

.allocate-navbar {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 24rpx;
  background-color: #fff;
}

.allocate-navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.allocate-navbar-back {
  width: 40rpx;
  height: 40rpx;
}

.allocate-navbar-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #000;
  flex: 1;
  text-align: center;
}

.allocate-navbar-right {
  width: 120rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.allocate-navbar-details {
  font-size: 28rpx;
  color: #000;
}

// 主要内容区域
.allocate-content {
  margin-top: calc(var(--status-bar-height) + 88rpx);
  padding: 24rpx;
  height: calc(100vh - var(--status-bar-height) - 88rpx);
  background-color: #fff;
}



// 创建者区域
.allocate-creator-section {
  margin-bottom: 48rpx;
  margin-top: 24rpx;
}

.allocate-section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #000;
  margin-bottom: 20rpx;
  display: block;
}

.allocate-creator-item {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 24rpx 0;
}

.allocate-creator-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  margin-left: 16rpx;
}

.allocate-creator-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.allocate-creator-avatar-icon {
  width: 40rpx;
  height: 40rpx;
}

.allocate-creator-name {
  font-size: 28rpx;
  color: #000;
  font-weight: 400;
  margin-bottom: 8rpx;
}

.allocate-creator-power {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 4rpx;
}

.allocate-creator-power-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.allocate-creator-power-value {
  font-size: 26rpx;
  color: #4CAF50;
  font-weight: 400;
}

// 成员区域
.allocate-members-section {
  margin-bottom: 48rpx;
}

.allocate-member-item {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.allocate-member-item:last-child {
  border-bottom: none;
}

.allocate-member-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  margin-left: 16rpx;
  margin-right: 24rpx;
}

.allocate-member-avatar {
  margin-right: 24rpx;
}

.allocate-member-avatar-img {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.allocate-member-avatar-bg {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.allocate-member-avatar-text {
  font-size: 32rpx;
  color: #999;
  font-weight: 500;
}

.allocate-member-name-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 8rpx;
}

.allocate-member-name {
  font-size: 28rpx;
  color: #000;
  font-weight: 400;
}

.allocate-member-arrow {
  width: 20rpx;
  height: 20rpx;
  margin-left: 12rpx;
  opacity: 0.6;
}

.allocate-member-power {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 4rpx;
}

.allocate-member-power-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.allocate-member-power-value {
  font-size: 26rpx;
  color: #4CAF50;
  font-weight: 400;
}

.allocate-member-controls {
  display: flex;
  flex-direction: row;
  align-items: center;
  border-radius: 6rpx;
  overflow: hidden;
  border: 1rpx solid #e0e0e0;
}

.allocate-member-minus,
.allocate-member-plus {
  width: 56rpx;
  height: 56rpx;
  background-color: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1rpx solid #e0e0e0;
}

.allocate-member-plus {
  border-right: none;
}

.allocate-member-minus-icon,
.allocate-member-plus-icon {
  width: 20rpx;
  height: 20rpx;
}

// 算力增加弹窗
.allocate-power-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 200;
  display: flex;
  align-items: center;
  justify-content: center;
}

.allocate-power-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.allocate-power-content {
  position: relative;
  width: 600rpx;
  background-color: #fff;
  border-radius: 24rpx;
  padding: 48rpx;
  margin: 0 48rpx;
  max-height: 80vh;
  overflow-y: auto;
}

// 弹窗头部
.allocate-power-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 48rpx;
}

.allocate-power-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #000;
  flex: 1;
  text-align: center;
}

.allocate-power-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.allocate-power-close-icon {
  width: 44rpx;
  height: 44rpx;
}

.allocate-power-placeholder {
  width: 60rpx;
  height: 60rpx;
}

// 算力信息
.allocate-power-info {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 48rpx;
}

.allocate-power-info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.allocate-power-info-value {
  font-size: 48rpx;
  color: #000;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.allocate-power-info-label {
  font-size: 26rpx;
  color: #666;
}

// 输入框
.allocate-power-input {
  margin-bottom: 48rpx;
}

.allocate-power-input-field {
  width: 100%;
  height: 120rpx;
  background-color: #fff;
  border: 1rpx solid #e0e0e0;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 32rpx;
  color: #000;
  text-align: center;
}

// 确定按钮
.allocate-power-confirm {
  width: 100%;
  height: 88rpx;
  background-color: #000;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.allocate-power-confirm-text {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}
</style>