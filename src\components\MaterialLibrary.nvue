<template>
  <view class="library-container" :style="{ paddingBottom: bottomAreaHeight + 'px' }">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input-wrapper">
        <image class="search-icon" src="@/asset/img/gallery/search.png" />
        <input class="search-input" v-model="searchKeyword" placeholder="搜索热门素材" placeholder-style="color:#666666" @input="onSearchInput" />
        <view class="search-actions">
          <view class="clear-search" v-if="searchKeyword" @click="clearSearch">
            <text class="clear-icon">×</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 素材分类标签栏 -->
    <scroll-view class="category-scroll" scroll-x="true" show-scrollbar="false">
      <view style="display: flex;flex-direction: row;">
        <view v-for="(category, index) in categories" :key="index" :class="['category-item', { 'category-active': currentCategory === category }]" @click="selectCategory(category)">
          <text :class="['category-text', { 'category-text-active': currentCategory === category }]">{{ category }}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 素材列表 - 使用 list 组件优化性能 -->
    <list class="material-list" @loadmore="loadMore" loadmoreoffset="10">
      <cell v-for="(row, rowIndex) in materialRows" :key="rowIndex">
        <view class="material-row">
          <view v-for="(item, itemIndex) in row" :key="itemIndex" :class="['material-item', { 'material-disabled': item.isDisabled }]" @click="item.isDisabled ? null : openPreview(item)">
            <!-- 图片圆角通过包装容器实现 -->
            <view class="material-image-wrapper">
              <image :src="item.coverUrl" class="material-image" mode="aspectFill" />
              <!-- 状态遮罩 -->
              <view v-if="item.isDisabled" class="material-status-overlay">
                <text class="status-text">{{ item.statusText }}</text>
              </view>
            </view>
            <view class="material-duration" v-if="item.duration">
              <text class="duration-text">{{ formatDuration(item.duration) }}</text>
            </view>
            <!-- 圆形选择按钮 -->
            <view v-if="!item.isDisabled" class="material-select-icon" :class="{ selected: item.selected }" @click.stop="toggleSelect(item)">
              <view class="badge" v-if="item.selected">
                <text class="badge-text">{{ selectedItems.findIndex(selected => selected.id === item.id) + 1 }}</text>
              </view>
            </view>
          </view>
        </view>
      </cell>

      <!-- 空状态提示 -->
      <cell v-if="materialList.length === 0 && !isLoading">
        <view class="empty-state">
          <text class="empty-title">暂无素材</text>
        </view>
      </cell>
      <cell v-if="isLoading">
        <view class="loading-more">
          <text class="loading-text">加载中...</text>
        </view>
      </cell>
    </list>

    <!-- 底部操作区域 -->
    <view class="bottom-selection-area">
      <!-- 选中的媒体项列表 -->
      <scroll-view :class="['selected-items-scroll', { active: selectedItems.length > 0 }]" :style="{ height: selectedItems.length > 0 ? '110px' : '0px' }" scroll-x="true" show-scrollbar="false">
        <view class="selected-items-container">
          <view v-for="(item, index) in selectedItems" :key="index" class="selected-item">
            <!-- 图片圆角通过包装容器实现 -->
            <view class="selected-item-image-wrapper">
              <image :src="item.coverUrl" class="selected-item-image" mode="aspectFill" />
            </view>
            <view class="selected-item-duration" v-if="item.duration">
              <text class="duration-text">{{ formatDuration(item.duration) }}</text>
            </view>
            <view class="selected-item-remove" @click="removeItem(index)">
              <text class="remove-icon">×</text>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 底部操作区域 -->
      <view class="bottom-actions">
        <view class="selection-tip" v-if="selectedItems.length === 0">
          <text class="selection-tip-text">请选择至少一个图片或者视频</text>
        </view>
        <view class="selection-tip" v-else>
          <text class="selection-tip-text">已选择 {{ selectedItems.length }} 个文件</text>
        </view>
        <view class="confirm-btn" @click="confirmSelection">
          <text class="confirm-btn-text">选好了</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 素材预览弹窗 -->
  <view v-if="showPreview" class="preview-modal">
    <view class="preview-content">
      <view class="preview-header">
        <text class="preview-title">素材预览</text>
        <view class="preview-close" @click="closePreview">
          <text class="close-icon">×</text>
        </view>
      </view>

      <view class="preview-body">
        <video v-if="previewItem && previewItem.type === 'video'" :src="previewItem.url || ''" class="preview-video" controls autoplay></video>
        <view v-else-if="previewItem" class="preview-image-wrapper">
          <image :src="previewItem.coverUrl" class="preview-image" mode="aspectFit"></image>
        </view>
      </view>

      <view class="preview-footer">
        <view class="preview-info">
          <text class="preview-name">{{ previewItem ? previewItem.name || '未命名素材' : '' }}</text>
          <text class="preview-duration" v-if="previewItem && previewItem.duration">时长: {{ formatDuration(previewItem.duration) }}</text>
        </view>

        <view class="preview-actions">
          <view class="preview-btn preview-select" @click="selectPreviewItem">
            <text class="btn-text">选择</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import materialService from '@/service/material'
import { debounce } from '@/utils/tools'

export default {
  name: 'MaterialLibrary',
  props: {
    // 是否可见
    visible: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // 素材库相关数据
      categories: ['最近', '收藏', '热门', '转场', '对口型', '背景'],
      currentCategory: '最近',
      materialList: [],
      selectedItems: [],
      page: 1,
      isLoading: false,
      hasMore: true,
      searchKeyword: '',
      debouncedSearch: null,
      showPreview: false,
      previewItem: null,
      // 布局相关数据
      screenHeight: 0,
      // 布局相关常量
      BOTTOM_ACTIONS_HEIGHT: 76, // 底部操作区域高度
      SELECTED_ITEMS_HEIGHT: 100, // 选中项预览区域高度
    }
  },
  created() {
    // 组件创建时加载素材数据
    this.loadMaterialData()
    this.debouncedSearch = debounce(this.searchMaterials, 300)
    // 获取系统信息
    this.getSystemInfo()
  },
  computed: {
    // 将素材列表按每3个一组进行分组
    materialRows() {
      const rows = []
      for (let i = 0; i < this.materialList.length; i += 3) {
        rows.push(this.materialList.slice(i, i + 3))
      }
      return rows
    },
    
    // 计算底部选择区域的总高度
    bottomAreaHeight() {
      if (this.selectedItems.length === 0) {
        // 无选中项时，只显示底部操作区域
        return this.BOTTOM_ACTIONS_HEIGHT
      } else {
        // 有选中项时，显示选中项预览区域 + 底部操作区域
        return this.SELECTED_ITEMS_HEIGHT + this.BOTTOM_ACTIONS_HEIGHT
      }
    }
  },
  // 监听选中项变化，优化布局更新
  watch: {
    selectedItems: {
      handler(newVal, oldVal) {
        // 当选中项数量变化时，触发布局重新计算
        console.log('选中项变化:', newVal.length, '->', oldVal.length)
        // 使用nextTick确保DOM更新后再执行
        this.$nextTick(() => {
          // 可以在这里添加额外的布局优化逻辑
        })
      },
      deep: true
    }
  },
  methods: {
    // 获取系统信息，适配不同屏幕
    getSystemInfo() {
      uni.getSystemInfo({
        success: (res) => {
          console.log('系统信息:', res)
          this.screenHeight = res.screenHeight || 667 // 默认iPhone 6/7/8高度
          this.statusBarHeight = res.statusBarHeight || 0
        },
        fail: (err) => {
          console.error('获取系统信息失败:', err)
          // 设置默认值
          this.screenHeight = 667
        }
      })
    },
    
    // 素材库相关方法
    async loadMaterialData() {
      this.isLoading = true

      try {
        const params = {
          page: this.page,
          page_size: 12
        }

        // 添加分类过滤
        if (this.currentCategory && this.currentCategory !== '最近') {
          params.category = this.currentCategory.toLowerCase()
        }

        // 添加搜索关键词
        if (this.searchKeyword) {
          params.search = this.searchKeyword
        }

        // 调用素材库API
        const response = await materialService.getMaterialLibrary(params)

        // 判断状态码
        if (response.status_code !== 1) {
          uni.showToast({ title: response.message || '获取素材失败', icon: 'none' })
          this.isLoading = false
          return
        }

        // 适配新返回结构
        const items = response?.data?.items || response?.items || []
        const pagination = response?.data?.pagination || response?.pagination || {}

        if (items && items.length) {
          // 转换API数据格式，兼容素材状态
          const materials = items.map(item => {
            // 处理素材状态
            let statusText = ''
            let isDisabled = false

            switch (item.status) {
              case 'uploaded':
                statusText = '已上传'
                break
              case 'transcoding':
                statusText = '转码中'
                isDisabled = true
                break
              case 'processing':
                statusText = '处理中'
                isDisabled = true
                break
              case 'completed':
                statusText = '完成'
                break
              case 'failed':
                statusText = '失败'
                isDisabled = true
                break
              default:
                statusText = '未知状态'
                break
            }

            return {
              id: item.id,
              coverUrl: item.cover_image_path_auth,
              url: item.file_path_auth,
              duration: item.duration,
              width: item.width,
              height: item.height,
              name: item.original_filename || '未命名素材',
              format: item.format,
              type: item.format === 'jpg' || item.format === 'png' ? 'image' : 'video',
              status: item.status,
              statusText: statusText,
              isDisabled: isDisabled,
              selected: false
            }
          })

          // 添加到现有列表或替换列表
          if (this.page === 1) {
            this.materialList = materials
          } else {
            this.materialList = [...this.materialList, ...materials]
          }

          // 更新分页信息
          this.hasMore = pagination.has_next
          this.page++
        } else {
          // 处理空结果
          if (this.page === 1) {
            this.materialList = []
          }
          this.hasMore = false
        }
      } catch (error) {
        console.error('Failed to load material data:', error)

        // API调用失败时使用模拟数据
        this.loadMockMaterialData()

        uni.showToast({
          title: '获取素材失败，已加载模拟数据',
          icon: 'none',
          duration: 2000
        })
      } finally {
        this.isLoading = false
      }
    },

    // 加载模拟素材数据（API调用失败时的备选方案）
    loadMockMaterialData() {
      const mockData = []
      for (let i = 0; i < 12; i++) {
        mockData.push({
          id: this.page * 100 + i,
          coverUrl: `https://picsum.photos/300/400?random=${this.page * 10 + i}`,
          duration: Math.floor(Math.random() * 120) + 5, // 5-125秒
          name: `素材 ${this.page}-${i}`,
          format: 'mp4',
          type: 'video',
          width: 1920,
          height: 1080,
          selected: false
        })
      }

      // 添加到现有列表或替换列表
      if (this.page === 1) {
        this.materialList = mockData
      } else {
        this.materialList = [...this.materialList, ...mockData]
      }

      this.page++
      this.hasMore = this.page < 4 // 模拟只有3页数据
    },

    // 刷新素材数据
    refreshMaterialData() {
      this.page = 1
      this.materialList = []
      this.loadMaterialData()
    },

    // 搜索素材
    searchMaterials() {
      this.page = 1
      this.materialList = []
      this.loadMaterialData()
    },

    // 清除搜索
    clearSearch() {
      this.searchKeyword = ''
      this.searchMaterials()
    },

    onSearchInput(e) {
      this.searchKeyword = e.detail.value
      this.debouncedSearch()
    },

    selectCategory(category) {
      if (this.currentCategory === category) return

      this.currentCategory = category
      this.page = 1
      this.hasMore = true

      this.refreshMaterialData()
    },

    loadMore() {
      if (this.isLoading || !this.hasMore) return
      this.loadMaterialData()
    },

    // 预览相关方法
    openPreview(item) {
      this.previewItem = { ...item }
      this.showPreview = true
    },

    closePreview() {
      this.showPreview = false
      this.previewItem = null
    },

    selectPreviewItem() {
      if (this.previewItem) {
        // 将预览项添加到选中列表
        const index = this.materialList.findIndex(material => material.id === this.previewItem.id)
        if (index !== -1) {
          this.materialList[index].selected = true
        }

        const selectedIndex = this.selectedItems.findIndex(selected => selected.id === this.previewItem.id)
        if (selectedIndex === -1) {
          this.selectedItems.push(this.previewItem)
          // 预览项已选中，设置selected属性为true
          this.previewItem.selected = true
        }

        this.closePreview()
      }
    },

    removeItem(index) {
      const item = this.selectedItems[index]

      // 更新原列表中项目的选中状态
      const materialIndex = this.materialList.findIndex(material => material.id === item.id)
      if (materialIndex !== -1) {
        this.materialList[materialIndex].selected = false
      }

      // 从选中列表移除
      this.selectedItems.splice(index, 1)
    },

    confirmSelection() {
      if (this.selectedItems.length === 0) {
        uni.showToast({
          title: '请至少选择一个素材',
          icon: 'none',
          duration: 2000
        })
        return
      }
      // 保存选中的素材到本地存储，以便返回页面可以获取
      uni.setStorageSync('selectedMaterials', this.selectedItems)
      // 触发事件，将选中的素材传递给父组件
      this.$emit('confirm', this.selectedItems)
    },

    // 工具方法
    formatDuration(seconds) {
      if (!seconds) return '00:00'
      const min = Math.floor(seconds / 60)
      const sec = Math.floor(seconds % 60)
      return `${min.toString().padStart(2, '0')}:${sec.toString().padStart(2, '0')}`
    },

    // 切换选中状态
    toggleSelect(item) {
      // 禁用的素材不能被选择
      if (item.isDisabled) {
        return
      }

      item.selected = !item.selected
      if (item.selected) {
        const index = this.selectedItems.findIndex(selected => selected.id === item.id)
        if (index === -1) {
          this.selectedItems.push(item)
        }
      } else {
        const index = this.selectedItems.findIndex(selected => selected.id === item.id)
        if (index !== -1) {
          this.selectedItems.splice(index, 1)
        }
      }
    },
  }
}
</script>

<style scoped>
/* 素材库样式 */
.library-container {
  flex: 1;
  /* padding-bottom 改为动态计算 */
}

/* 搜索框样式优化 */
.search-bar {
  padding: 10px 16px;
  background-color: #1a1a1a;
}

.search-input-wrapper {
  flex-direction: row;
  align-items: center;
  background-color: #2a2a2a;
  border-radius: 18px;
  padding: 6px 12px;
  height: 36px;
}

.search-icon {
  width: 14px;
  height: 14px;
  margin-right: 8px;
}

.search-input {
  flex: 1;
  height: 36px;
  font-size: 14px;
  flex-direction: row;
  align-items: center;
  margin-left: 10px;
  color: #fff;
}

.clear-search {
  width: 24px;
  height: 24px;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
}

.clear-icon {
  color: #888888;
  font-size: 16px;
}

.search-btn {
  background-color: #ff2d55;
  border-radius: 6px;
  padding: 10px 20px;
}

.search-btn-text {
  color: #ffffff;
  font-size: 15px;
  font-weight: 600;
}

.category-scroll {
  background-color: #2a2a2a;
  height: 44px;
  flex-direction: row;
  padding: 0 8px;
}

.category-item {
  padding: 0 16px;
  height: 44px;
  justify-content: center;
  align-items: center;
}

.category-active {
  border-bottom-width: 2px;
  border-bottom-color: #ff0043;
}

.category-text {
  color: #cccccc;
  font-size: 14px;
}

.category-text-active {
  color: #ffffff;
  font-weight: bold;
}

/* 素材列表优化 - 使用 list 组件 */
.material-list {
  flex: 1;
  background-color: #fff;
  padding-top: 10px;
}

/* 素材行布局 */
.material-row {
  flex-direction: row;
  padding: 0 12px 8px 12px;
  justify-content: space-between;
}

.material-item {
  width: 230rpx;
  height: 120px;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

/* 图片圆角通过包装容器实现 */
.material-image-wrapper {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.material-image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

/* 禁用状态的素材样式 */
.material-disabled {
  opacity: 0.6;
  pointer-events: none;
}

.material-disabled .material-image-wrapper {
  pointer-events: auto;
}

/* 状态遮罩样式 */
.material-status-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  justify-content: center;
  align-items: center;
  border-radius: 8px;
}

.status-text {
  color: #ffffff;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
}

/* 素材标记调优 */
.material-duration {
  position: absolute;
  right: 18px;
  bottom: 22px;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 2px;
  padding: 1px 4px;
}

.duration-text {
  color: #ffffff;
  font-size: 12px;
  line-height: 16px;
}

/* 红标签样式 */
.badge {
  background-color: #ff0043;
  border-radius: 10px;
}

.badge-text {
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
  line-height: 20px;
  width: 20px;
  height: 20px;
}

/* 选择标记调优 */
.material-select-icon {
  position: absolute;
  top: 4px;
  right: 20px;
  width: 20px;
  height: 20px;
  background-color: transparent;
  border-width: 1px;
  border-color: #ffffff;
  border-radius: 10px;
  justify-content: center;
  align-items: center;
  z-index: 0;
}

.material-select-icon.selected {
  background-color: rgba(255, 0, 67, 0.7);
  border-color: transparent;
}

.select-circle {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: #ffffff;
}

.loading-more {
  padding: 16px 0;
  justify-content: center;
  align-items: center;
}

.loading-text {
  color: #888888;
  font-size: 14px;
}

/* 底部选择确认区域样式 */
.bottom-selection-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #1a1a1a;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding: 16px 20px 10px 20px;
}

.selected-items-scroll {
  margin-bottom: 8px;
  height: 110px;
  flex-direction: row;
  align-items: flex-start;
  background-color: transparent;
}
.selected-items-container {
  flex-direction: row;
  align-items: flex-start;
  height: 100px;
  padding: 0;
}

.selected-items-scroll.active {
  opacity: 1;
}

.selected-item {
  position: relative;
  width: 80px;
  height: 100px;
  border-radius: 6px;
  margin-right: 12px;
}

/* 选中项图片圆角通过包装容器实现 */
.selected-item-image-wrapper {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  /* overflow: visible; 保证角标不被裁剪 */
}

.selected-item-image {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  z-index: 1;
}

.selected-item-duration {
  position: absolute;
  bottom: 2px;
  left: 2px;
  background-color: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  font-size: 11px;
  padding: 1px 4px;
  border-radius: 3px;
  z-index: 10;
  justify-content: center;
  align-items: center;
}

.selected-item-remove {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 20px;
  height: 20px;
  background-color: #ffffff;
  border-radius: 10px;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.remove-icon {
  color: #000000;
  font-size: 16px;
  font-weight: bold;
}

.bottom-actions {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0 5px;
}

.selection-tip {
  flex: 1;
}

.selection-tip-text {
  color: #ffffff;
  font-size: 13px;
  opacity: 0.9;
}

.confirm-btn {
  background-color: #ff2d55;
  border-radius: 6px;
  padding: 10px 20px;
  margin-left: 16px;
}

.confirm-btn-text {
  color: #ffffff;
  font-size: 15px;
  font-weight: 600;
}

/* 预览弹窗样式 - 使用nvue兼容的flex布局 */
.preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  justify-content: center;
  align-items: center;
}

.preview-content {
  width: 600rpx;
  height: 400px;
  background-color: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
  flex-direction: column;
}

.preview-header {
  height: 60px;
  padding: 16px 20px;
  border-bottom-width: 1px;
  border-bottom-color: #333333;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.preview-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
}

.preview-close {
  width: 32px;
  height: 32px;
  justify-content: center;
  align-items: center;
}

.close-icon {
  color: #ffffff;
  font-size: 24px;
  font-weight: bold;
}

.preview-body {
  flex: 1;
  padding: 12px;
  justify-content: center;
  align-items: center;
}

.preview-video {
  width: 500rpx;
  height: 220px;
  border-radius: 8px;
}

.preview-image-wrapper {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.preview-footer {
  height: 60px;
  padding: 16px 20px;
  border-top-width: 1px;
  border-top-color: #333333;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.preview-info {
  flex: 1;
  overflow: hidden;
}

.preview-name {
  width: 300rpx;
  color: #ffffff;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  lines: 1;
}

.preview-duration {
  color: #888888;
  font-size: 13px;
}

.preview-actions {
  flex-direction: row;
}

.preview-btn {
  padding: 10px 20px;
  border-radius: 6px;
  margin-left: 10px;
}

.preview-cancel {
  background-color: #333333;
  border-width: 1px;
  border-color: #555555;
}

.preview-select {
  background-color: #ff2d55;
}

.btn-text {
  color: #ffffff;
  font-size: 15px;
  font-weight: 600;
}

/* 空状态提示样式 */
.empty-state {
  align-items: center;
  padding: 40px 0;
}

.empty-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 10px;
}

.empty-title {
  color: #888888;
  font-size: 18px;
  margin-bottom: 5px;
}


</style>
