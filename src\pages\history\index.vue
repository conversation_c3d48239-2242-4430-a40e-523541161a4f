<template>
  <view class="history-page">
    <view class="status-bar"></view>
    <!-- 顶部自定义导航栏和背景 -->
    <view class="history-header">
      <view class="history-header-bar">
        <text class="history-header-title">创作历史</text>
        <text class="history-header-manage" @click="handleManage">管理</text>
      </view>
      <view class="history-header-tabs">
        <text :class="['history-header-tab', activeTab === 0 ? 'history-header-tab--active' : '']" @click="changeTab(0)">全部</text>
        <text :class="['history-header-tab', activeTab === 1 ? 'history-header-tab--active' : '']" @click="changeTab(1)">视频</text>
        <text :class="['history-header-tab', activeTab === 2 ? 'history-header-tab--active' : '']" @click="changeTab(2)">穿版</text>
      </view>
      <view class="history-header-filters">
        <!-- 日期选择器 -->
        <view class="history-header-filter-picker">
          <uni-datetime-picker
            type="daterange"
            v-model="dateFilter"
            :start="datePickerStart"
            :end="datePickerEnd"
            @change="onDatePickerConfirm"
            @maskClick="onDatePickerClose"
            @show="openDatePicker"
            :clear-icon="false"
            class="custom-datetime-picker"
          />
        </view>
        <!-- 状态筛选 -->
        <view class="history-header-filter-wrapper">
          <view :class="['history-header-filter', statusFilter !== null ? 'history-header-filter--active' : '']" @click="showStatusDropdown = !showStatusDropdown">
            <text>{{ statusFilterLabel }}</text>
            <image class="history-header-filter-icon" :src="statusFilter !== null ? tabSelected : tabNoselect" />
          </view>
          <view v-if="showStatusDropdown" class="status-dropdown">
            <view class="status-dropdown-item" :class="{ 'status-dropdown-item--active': statusFilter === null }" @click="selectStatusFilter(null)">全部</view>
            <view class="status-dropdown-item" v-for="item in statusFilterOptions.slice(1)" :key="item.value" :class="{ 'status-dropdown-item--active': statusFilter == item.value }" @click="selectStatusFilter(item.value)">{{ item.label }}</view>
          </view>
          <view v-if="showStatusDropdown" class="status-dropdown-mask" @click="showStatusDropdown = false"></view>
        </view>
      </view>
    </view>

    <!-- 可滚动内容区域 -->
    <scroll-view 
      :key="scrollViewKey"
      class="history-scroll" 
      scroll-y="true" 
      refresher-enabled="true" 
      :refresher-triggered="refreshing" 
      @refresherrefresh="onRefresh"
      show-scrollbar="false"
      enhanced="true"
      bounces="true"
      @scrolltolower="onScrollToLower"
      :scroll-top="scrollTop"
      @scroll="onScroll"
    >
      <view class="history-content">
        <!-- 历史分组列表 -->
        <view class="history-list">
          <view v-for="(group, gIdx) in historyList" :key="group.id" class="history-group">
            <text class="history-group__date">{{ formatTime(group.create_time, 'YYYY-MM-DD') }}</text>
            <!-- 主卡片区 -->
            <view class="history-group__maincard">
              <view class="history-group__maincard-header">
                <text class="history-group__maincard-title">{{ group.title }}</text>
                <text class="history-group__maincard-time">{{ formatTime(group.create_time) }}</text>
              </view>
              <view class="history-group__maincard-content">
                <view class="history-group__maincard-img-box"  @click="handlePlayVideo(group)">
                  <image class="history-group__maincard-img" :src="group.cover_image_path_auth || defaultPoster" />
                  <image class="history-group__maincard-play-icon" v-if="group.output_path_auth" src="@/asset/img/history/card_main.png"></image>
                </view>
                <view class="history-group__maincard-desc">
                  <view class="history-group__maincard-desc-text">{{ group.user_script }}</view>
                  <!-- 进度条状态显示 -->
                  <view v-if="group.status !== 4 && group.status !== 5" class="history-group__maincard-status--progress">
                    <view class="history-group__maincard-progress-container">
                      <!-- 删除按钮 -->
                      <view class="history-group__maincard-btn--delete-progress">
                        <image class="history-group__maincard-delete-img" @click="deleteHistory(group.id)" src="@/asset/img/history/delete.png"></image>
                      </view>
                      <view class="history-group__maincard-progress-bar">
                        <!-- 待处理状态 -->
                        <view v-if="group.status === 0" class="history-group__maincard-progress-text--no-fill">
                          <text>{{ getProgressText(group.status) }}</text>
                        </view>
                        <!-- 处理中状态-->
                        <view v-else class="history-group__maincard-progress-fill" :style="{ width: getProgressWidth(group.status) }">
                          <text class="history-group__maincard-progress-text">{{ getProgressText(group.status) }}</text>
                        </view>
                      </view>
                    </view>
                  </view>
                  <!-- 完成状态 -->
                  <view v-else-if="group.status === 4" class="history-group__maincard-status--completed">
                    <view class="history-group__maincard-btn--delete">
                      <image class="history-group__maincard-delete-img" @click="deleteHistory(group.id)" src="@/asset/img/history/delete.png"></image>
                    </view>
                    <view class="history-group__maincard-btn" @click="() => handleDownload(group)">下载</view>
                    <view class="history-group__maincard-btn">数据</view>
                    <view class="history-group__maincard-btn history-group__maincard-btn--publish" @click="() => handleMaincardAction('publish', group)">发布</view>
                  </view>
                  <!-- 失败状态 -->
                  <view v-else-if="group.status === 5" class="history-group__maincard-status--completed">
                    <view class="history-group__maincard-btn--delete">
                      <image class="history-group__maincard-delete-img" @click="deleteHistory(group.id)" src="@/asset/img/history/delete.png"></image>
                    </view>
                    <view class="fail-text">生成失败</view>
                    <view class="history-group__maincard-btn history-group__maincard-btn--publish" @click="() => handleMaincardAction('retry', group)">重试</view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 卡片区 -->
            <!-- <view class="history-group__items" v-if="group.items">
              <view v-for="(item, idx) in group.items" :key="idx" class="history-group__item">
                <image class="history-group__item-img" :src="item.img" mode="aspectFill" />
                <view v-if="item.status === 'generating'" class="history-group__item-status--generating">
                  <image class="history-group__item-status-img" src="@/asset/img/history/card_image.png"></image>
                  <text>图片生成中…</text>
                  <text class="history-group__item-status-time">{{ item.estimatedTime }}</text>
                </view>
                <view v-else-if="item.status === 'completed'">
                  <image src="@/asset/img/history/card_video.png" class="history-group__item-play-icon"></image>
                </view>
                <view v-else-if="item.status === 'wait'" class="history-group__item-status--wait">
                  <text class="history-group__item-status-tag">待生成</text>
                  <text class="history-group__item-status-count">{{ item.count }}张</text>
                </view>
                <view v-if="item.duration" class="history-group__item-duration">{{ item.duration }}</view>
                <image v-if="item.status !== 'generating'" src="@/asset/img/history/card_count_bg.png" class="history-group__item-mask"></image>
              </view>
            </view> -->
          </view>
        </view>

        <view class="history-nomore" v-if="!pagination.hasNext || historyList.length === 0">
          <text>暂时没有更多了</text>
        </view>
      </view>
    </scroll-view>

    <!-- 浮动创作按钮 -->
    <view class="history-create-btn-fixed" @click="goToCreate">
      <image class="history-create-btn-fixed__img" src="@/asset/img/history/btn_create.png" />
      <text class="history-create-btn-fixed__text">创作</text>
    </view>

    <!-- 分享弹窗 -->
    <view v-if="showDialog" class="history-publish-modal__mask" @click="closePublishModal" @touchmove.stop.prevent></view>
    <view v-if="showDialog" class="history-publish-modal__container" @touchmove.stop.prevent>
      <view class="history-publish-modal__title">分享至：</view>
      <view class="history-publish-modal__grid">
        <view class="history-publish-modal__item" v-for="(item, idx) in publishChannelsFull.slice(0,4)" @click="handleModalItemClick(item)" :key="item.key">
          <image class="history-publish-modal__icon" :src="item.icon" mode="aspectFit" />
          <text class="history-publish-modal__text">{{ item.text }}</text>
        </view>
      </view>
      <view class="history-publish-modal__grid">
        <view class="history-publish-modal__item" v-for="(item, idx) in publishChannelsFull.slice(4)" @click="handleModalItemClick(item)" :key="item.key">
          <image class="history-publish-modal__icon" :src="item.icon" mode="aspectFit" />
          <text class="history-publish-modal__text">{{ item.text }}</text>
        </view>
      </view>
      <image class="history-publish-modal__divider" src="@/asset/img/history/dialog-divider.png" />
      <view class="history-publish-modal__cancel" @click="closePublishModal">取消</view>
    </view>

                <!-- VideoPlayer 组件 -->
            <VideoPlayer
              v-if="showVideoPlayer"
              :video-src="videoUrl"
              :poster="videoPoster"
              :auto-play="true"
              :loop="false"
              :muted="false"
              :has-modal="showDownloadProgress"
              @close="onVideoPlayerClose"
              @play="onVideoPlay"
              @pause="onVideoPause"
              @ended="onVideoEnded"
              @timeupdate="onVideoTimeUpdate"
              @fullscreen="onVideoFullscreen"
              @export="onVideoExport"
              @error="onVideoError"
            />

            <!-- 下载进度组件 -->
            <DownloadProgress
              :visible="showDownloadProgress"
              :progress="downloadProgress"
              :file-name="downloadFileName"
              :status="downloadStatus"
              @cancel="handleCancelDownload"
            />
  </view>
  
  <!-- VideoPreviewExport 组件 -->
  <!-- <VideoPreviewExport
    v-if="showVideoPreview"
    :videoUrl="videoUrl"
    :videoTitle="videoTitle"
    :videoId="videoId"
    @close="onVideoPreviewClose"
  /> -->
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { onShow, onHide } from '@dcloudio/uni-app'
import historyService from '../../service/history';
import { formatTime } from '@/utils/tools';
import douyin from '@/asset/img/history/dialog-douyin.png';
import wechat from '@/asset/img/history/dialog-wechat.png';
import xiaohongshu from '@/asset/img/history/dialog-xiaohongshu.png';
import other from '@/asset/img/history/dialog-other.png';
import download from '@/asset/img/history/dialog-download.png';
import preview from '@/asset/img/history/dialog-preview.png';
import copy from '@/asset/img/history/dialog-copy.png';
import edit from '@/asset/img/history/dialog-edit.png';
import cardMainBg from '@/asset/img/history/card_main_bg.png';
import cardGeneratingBg2 from '@/asset/img/history/card_generating_bg2.png';
import cardStatusBg2 from '@/asset/img/history/card_status_bg2.png';
import cardVideoBg from '@/asset/img/history/card_video_bg.png';
import tabSelected from '@/asset/img/history/tab_selected.png';
import tabNoselect from '@/asset/img/history/tab_noselect.png';
import defaultPoster from '@/asset/img/history/default_poster.png';
import VideoPreviewExport from '@/components/VideoPreviewExport.vue'
import VideoPlayer from '@/components/business/VideoPlayer/index.vue'
import uniDatetimePicker from '@dcloudio/uni-ui/lib/uni-datetime-picker/uni-datetime-picker.vue'  
import DownloadProgress from '@/components/business/DownloadProgress/index.vue'

const videoUrl = ref('')
const videoPoster = ref('')
const videoId = ref(null)
const showVideoPlayer = ref(false)

// 下载进度相关变量
const showDownloadProgress = ref(false)
const downloadProgress = ref(0)
const downloadFileName = ref('')
const downloadStatus = ref('downloading')
const currentDownloadTask = ref(null)
const scrollTop = ref(0)
const activeTab = ref(0)
const refreshing = ref(false)
const scrollViewKey = ref(0) // 用于强制重新渲染 scroll-view
const historyGroups = ref([
  {
    date: Date.now(),
    main: {
      title: '一键成片-引流-口播',
      time: '2025-05-07 20:49',
      desc: '在深圳龙华当你老了你会如何回答以下…',
      img: cardMainBg,
      generating: true,
    },
    items: [
      { img: cardGeneratingBg2, status: 'generating', estimatedTime: '预计还需10分钟' },
      { img: cardGeneratingBg2, status: 'generating', estimatedTime: '预计还需10分钟' },
      { img: cardStatusBg2, status: 'wait', count: 4 },
      { img: cardVideoBg, status: 'completed', duration: '00:12' },
      { img: cardStatusBg2, status: 'wait', count: 4 },
    ]
  },
  {
    date: Date.now(),
    main: {
      title: 'AI混剪-短视频',
      time: '2025-05-06 18:30',
      desc: 'AI混剪自动生成短视频内容',
      img: cardMainBg,
      generating: false,
    },
    items: [
      { img: cardStatusBg2, status: 'completed' },
      { img: cardVideoBg, status: 'wait', count: 2 },
      { img: cardGeneratingBg2, status: 'generating', estimatedTime: '预计还需5分钟' }
    ]
  },
  {
    date: Date.now(),
    main: {
      title: 'AI穿版-图片生成',
      time: '2025-05-05 15:20',
      desc: 'AI穿版批量生成图片',
      img: cardMainBg,
      generating: false,
    },
    items: [
      { img: cardStatusBg2, status: 'wait', count: 1 },
      { img: cardVideoBg, status: 'completed', duration: '00:08' }
    ]
  }
])
const loading = ref(false)
const showDialog = ref(false)
// 当前操作视频
const currentOperateVideo = ref(null)
// 创作历史列表
const historyList = ref([])
const pagination = ref({
  page: 1,
  size: 10,
  hasNext: false
})
// 轮询相关变量
const pollingTimer = ref(null)
const isPolling = ref(false)

// 进度条状态配置
const progressConfig = {
  0: { text: '待处理... 0%', percent: 0, width: '0%' },
  1: { text: '剪辑中... 25%', percent: 25, width: '25%' },
  2: { text: '剪辑中... 50%', percent: 50, width: '50%' },
  3: { text: '剪辑中... 75%', percent: 75, width: '75%' },
  4: { text: '已完成 100%', percent: 100, width: '100%' },
  5: { text: '生成失败 0%', percent: 0, width: '0%' }
}

// 进度条相关函数
const getProgressText = (status) => {
  return progressConfig[status]?.text || '正在加载...'
}

const getProgressWidth = (status) => {
  return progressConfig[status]?.width || '0%'
}

// 检查是否需要继续轮询
const needContinuePolling = () => {
  return historyList.value.some(item => {
    // 如果状态不是完成(4)和失败(5)，则需要继续轮询
    return item.status !== 4 && item.status !== 5
  })
}

// 开始轮询
const startPolling = () => {
  if (isPolling.value) return
  
  isPolling.value = true
  const poll = async () => {
    if (!isPolling.value || !needContinuePolling()) {
      stopPolling()
      return
    }
    
    try {
      await fetchCreateHistroyData()
    } catch (error) {
      console.error('轮询更新失败:', error)
    }
    
    // 如果还在轮询状态，继续下一次轮询
    if (isPolling.value && needContinuePolling()) {
      pollingTimer.value = setTimeout(poll, 8000)
    } else {
      stopPolling()
    }
  }
  
  // 启动第一次轮询
  pollingTimer.value = setTimeout(poll, 8000)
}

// 停止轮询
const stopPolling = () => {
  if (pollingTimer.value) {
    clearTimeout(pollingTimer.value)
    pollingTimer.value = null
  }
  isPolling.value = false
}

const publishChannelsFull = [
  { icon: douyin, text: '抖音', key: 'douyin' },
  { icon: wechat, text: '微信', key: 'wechat' },
  { icon: xiaohongshu, text: '小红书', key: 'xiaohongshu' },
  { icon: other, text: '其他', key: 'other' },
  { icon: download, text: '下载本地', key: 'download' },
  { icon: preview, text: '预览视频', key: 'preview' },
  { icon: copy, text: '复制视频', key: 'copy' },
  { icon: edit, text: '编辑视频', key: 'edit' }
]

function handleDownload(group) {
  if (!group || !group.vod_video_id) {
    uni.showToast({ title: '无可下载文件', icon: 'none' });
    return;
  }
  
  // 先检查相册权限
  checkPhotoAlbumPermission().then(hasPermission => {
    if (hasPermission) {
      // 有权限，直接开始下载
      startDownload(group);
    } else {
      // 没有权限，弹窗索要权限
      requestPhotoAlbumPermission().then(granted => {
        if (granted) {
          // 获取权限成功，开始下载
          startDownload(group);
        } else {
          // 用户拒绝权限
          uni.showToast({ title: '需要相册权限才能保存视频', icon: 'none' });
        }
      });
    }
  });
}

// 检查相册权限
function checkPhotoAlbumPermission() {
  return new Promise((resolve) => {
    // #ifdef APP-PLUS
    // APP端检查存储权限
    try {
      if (typeof plus !== 'undefined' && plus.android) {
        const main = plus.android.runtimeMainActivity();
        const PackageManager = plus.android.importClass('android.content.pm.PackageManager');
        const permission = 'android.permission.WRITE_EXTERNAL_STORAGE';
        
        const granted = main.checkSelfPermission(permission) === PackageManager.PERMISSION_GRANTED;
        resolve(granted);
      } else {
        resolve(false);
      }
    } catch (error) {
      console.error('权限检查失败:', error);
      resolve(false);
    }
    // #endif
    
    // #ifdef H5
    // H5环境下默认有权限
    resolve(true);
    // #endif
    
    // #ifdef MP-WEIXIN
    // 微信小程序检查相册权限
    uni.getSetting({
      success: (res) => {
        if (res.authSetting['scope.writePhotosAlbum'] === false) {
          resolve(false);
        } else if (res.authSetting['scope.writePhotosAlbum'] === true) {
          resolve(true);
        } else {
          uni.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => resolve(true),
            fail: () => resolve(false)
          });
        }
      },
      fail: () => resolve(false)
    });
    // #endif
    
    // 其他平台默认有权限
    // #ifndef APP-PLUS
    // #ifndef H5
    // #ifndef MP-WEIXIN
    resolve(true);
    // #endif
    // #endif
    // #endif
  });
}

// 请求相册权限
function requestPhotoAlbumPermission() {
  return new Promise((resolve) => {
    uni.showModal({
      title: '需要相册权限',
      content: '为了保存视频到相册，需要您授权相册访问权限。请在设置中开启"保存到相册"权限。',
      confirmText: '去设置',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // #ifdef APP-PLUS
          // APP端直接请求权限
          if (typeof plus !== 'undefined' && plus.android) {
            const permission = 'android.permission.WRITE_EXTERNAL_STORAGE';
            plus.android.requestPermissions([permission], (resultObj) => {
              if (resultObj.granted.length > 0) {
                uni.showToast({ title: '权限获取成功', icon: 'success' });
                resolve(true);
              } else {
                uni.showToast({ title: '权限获取失败', icon: 'none' });
                resolve(false);
              }
            }, (error) => {
              uni.showToast({ title: '权限获取失败', icon: 'none' });
              resolve(false);
            });
          } else {
            uni.showToast({ title: '当前环境不支持权限请求', icon: 'none' });
            resolve(false);
          }
          // #endif
          
          // #ifdef H5
          resolve(true);
          // #endif
          
          // #ifdef MP-WEIXIN
          // 微信小程序打开设置页面
          uni.openSetting({
            success: (settingRes) => {
              if (settingRes.authSetting['scope.writePhotosAlbum'] === true) {
                uni.showToast({ title: '权限获取成功', icon: 'success' });
                resolve(true);
              } else {
                uni.showToast({ title: '请在设置中开启相册权限', icon: 'none' });
                resolve(false);
              }
            },
            fail: () => {
              uni.showToast({ title: '打开设置失败', icon: 'none' });
              resolve(false);
            }
          });
          // #endif
          
          // 其他平台默认有权限
          // #ifndef APP-PLUS
          // #ifndef H5
          // #ifndef MP-WEIXIN
          resolve(true);
          // #endif
          // #endif
          // #endif
        } else {
          resolve(false);
        }
      }
    });
  });
}

// 开始下载流程
function startDownload(group) {
  // 设置下载信息
  downloadFileName.value = group.title || '视频文件'
  downloadProgress.value = 0
  downloadStatus.value = 'downloading'
  showDownloadProgress.value = true
  
  // 获取下载链接
  historyService.getOriginalVodDownloadUrl(group.vod_video_id)
    .then(res => {
      if (res.status_code === 1 && res.data && res.data.download_url) {
        console.log(res.data.download_url, 'download_url');
        
        // 开始下载
        const downloadTask = uni.downloadFile({
          url: res.data.download_url,
          success: (res) => {
            if (res.statusCode === 200) {
              downloadStatus.value = 'completed'
              downloadProgress.value = 100
              
              // 直接保存到相册
              uni.saveVideoToPhotosAlbum({
                filePath: res.tempFilePath,
                success: () => {
                  setTimeout(() => {
                    showDownloadProgress.value = false
                    uni.showToast({ title: '已保存到相册', icon: 'success' });
                  }, 1000)
                },
                fail: (err) => {
                  downloadStatus.value = 'failed'
                  setTimeout(() => {
                    showDownloadProgress.value = false
                    if (err.errMsg && err.errMsg.includes('auth deny')) {
                      uni.showModal({
                        title: '提示',
                        content: '需要您授权保存到相册',
                        success: (modalRes) => {
                          if (modalRes.confirm) {
                            uni.openSetting()
                          }
                        }
                      })
                    } else {
                      uni.showToast({ title: '保存到相册失败', icon: 'none' });
                    }
                  }, 1000)
                }
              });
            } else {
              downloadStatus.value = 'failed'
              setTimeout(() => {
                showDownloadProgress.value = false
                uni.showToast({ title: '下载失败', icon: 'none' });
              }, 2000)
            }
          },
          fail: (err) => {
            console.error('下载失败:', err)
            downloadStatus.value = 'failed'
            setTimeout(() => {
              showDownloadProgress.value = false
              uni.showToast({ title: '下载失败', icon: 'none' });
            }, 2000)
          }
        })
        
        // 监听下载进度
        downloadTask.onProgressUpdate((res) => {
          downloadProgress.value = res.progress
        })
        
        // 保存下载任务引用，用于取消
        currentDownloadTask.value = downloadTask
        
      } else {
        showDownloadProgress.value = false
        uni.showToast({ title: res.message || '获取下载链接失败', icon: 'none' });
      }
    })
    .catch((err) => {
      console.error('获取下载链接失败:', err)
      showDownloadProgress.value = false
      uni.showToast({ title: '网络错误', icon: 'none' });
    });
}



function handlePlayVideo(group) {
  if (!group.output_path_auth) {
    uni.showToast({ title: '暂无视频', icon: 'none' });
    return;
  }
  // 隐藏tabbar
  uni.hideTabBar()
  videoUrl.value = group.output_path_auth
  videoPoster.value = group.cover_image_path_auth || defaultPoster
  videoId.value = group.vod_video_id
  showVideoPlayer.value = true
}

function onVideoPlayerClose() {
  console.log('History: VideoPlayer关闭事件触发')
  showVideoPlayer.value = false
  videoUrl.value = ''
  videoPoster.value = ''
  // 确保下载进度弹窗也关闭
  showDownloadProgress.value = false
}

// VideoPlayer 事件处理函数
function onVideoPlay() {
  console.log('视频开始播放')
}

function onVideoPause() {
  console.log('视频暂停播放')
}

function onVideoEnded() {
  console.log('视频播放结束')
}

function onVideoTimeUpdate({ current, total }) {
  console.log(`播放进度: ${current}/${total}`)
}

function onVideoFullscreen(isFullscreen) {
  console.log(`全屏状态: ${isFullscreen}`)
}

function onVideoExport() {
  // 使用当前视频ID进行导出操作
  if (videoId.value) {
    handleDownload({ vod_video_id: videoId.value })
  }
}

function onVideoError(error) {
  console.error('视频播放错误:', error)
  uni.showToast({ title: '视频播放失败', icon: 'none' })
}

// 取消下载处理
function handleCancelDownload() {
  if (currentDownloadTask.value) {
    currentDownloadTask.value.abort()
    currentDownloadTask.value = null
  }
  
  downloadStatus.value = 'cancelled'
  setTimeout(() => {
    showDownloadProgress.value = false
    uni.showToast({ title: '下载已取消', icon: 'none' })
  }, 1000)
}


function handlePreview(group) {
  showDialog.value = false;
  handlePlayVideo(group);
}

const modalItemHandlerMap = {
  download: handleDownload,
  preview: handlePreview
}

// 分享弹窗点击事件
function handleModalItemClick(item) {
  if (modalItemHandlerMap[item.key]) {
    modalItemHandlerMap[item.key](currentOperateVideo.value)
  }
}

// 开始创作
function goToCreate() {
  uni.navigateTo({ url: '/pages/create/index' })
}

const closePublishModal = () => { 
  showDialog.value = false;
  uni.showTabBar()
}

const handleManage = () => {}

const changeTab = async (index) =>{
  activeTab.value = index;
  scrollTop.value = 0;
  // 停止当前轮询
  stopPolling();
  uni.showLoading({ title: '加载中...' , mask: true});
  await fetchCreateHistroyData();
  uni.hideLoading();
}

const handleMaincardAction = (action, group) => {
  currentOperateVideo.value = group;
  if (action === 'publish') {
    showDialog.value = true;
    uni.hideTabBar();
  } else if (action === 'retry') {
    // 重试逻辑
    uni.showLoading({ title: '重试中...', mask: true });
    historyService.retryCreateHistory(group.id)
      .then(res => {
        if (res.status_code === 1) {
          uni.showToast({ title: res.data?.message || '重试成功', icon: 'success' });
          fetchCreateHistroyData();
        } else {
          uni.showToast({ title: res.message || '重试失败', icon: 'none' });
        }
      })
      .catch(() => {
        uni.showToast({ title: '网络错误', icon: 'none' });
      })
      .finally(() => {
        uni.hideLoading();
      });
  }
}

const showStatusDropdown = ref(false)
const statusFilter = ref(null)
const statusFilterOptions = [
  { label: '全部', value: null },
  { label: '待处理', value: 0 },
  { label: '剪辑中', value: 3 },
  { label: '生成失败', value: 5 },
  { label: '已完成', value: 4 },
]
const statusFilterLabel = computed(() => {
  const found = statusFilterOptions.find(i => i.value === statusFilter.value)
  return found ? found.label : '全部';
})

function selectStatusFilter(val) {
  if (loading.value) return; // 加载中禁止切换
  if (statusFilter.value === val) {
    showStatusDropdown.value = false
    return
  }
  statusFilter.value = val
  showStatusDropdown.value = false
  // 停止当前轮询
  stopPolling();
  uni.showLoading({ title: '加载中...', mask: true })
  fetchCreateHistroyData().finally(() => {
    scrollTop.value = 0;
    uni.hideLoading()
  })
}

// 获取今天和7天前的日期字符串
function getDateStr(date) {
  return date.toISOString().slice(0, 10)
}
const today = new Date()
const sevenDaysAgo = new Date()
sevenDaysAgo.setDate(today.getDate() - 6) // 包含今天共7天

const dateFilter = ref([getDateStr(sevenDaysAgo), getDateStr(today)])
const datePickerStart = '2020-01-01'
const datePickerEnd = new Date().toISOString().slice(0, 10)

function openDatePicker() {
  uni.hideTabBar()
}
function closeDatePicker() {
  uni.showTabBar()
}
function onDatePickerConfirm(val) {
  closeDatePicker()
  if (val && val.length === 2) {
    dateFilter.value = val
    stopPolling()
    uni.showLoading({ title: '加载中...', mask: true })
    fetchCreateHistroyData().finally(() => {
      scrollTop.value = 0
      uni.hideLoading()
    })
  }
}
function onDatePickerClose() {
  closeDatePicker()
}

// 获取创作历史列表
const fetchCreateHistroyData = async (isLoadMore = false) => {
  if (loading.value) return;
  loading.value = true;

  // 如果是加载更多，页码+1，否则重置为1
  if (isLoadMore) {
    pagination.value.page += 1;
  } else {
    pagination.value.page = 1;
  }

  const params = {
    page: pagination.value.page,
    page_size: pagination.value.size
  }
  if (statusFilter.value !== null) {
    params.status = statusFilter.value
  }
  if (dateFilter.value && dateFilter.value[0] && dateFilter.value[1]) {
    params.start_date = dateFilter.value[0]
    params.end_date = dateFilter.value[1]
  }

  try {
    const res = await historyService.getCreateHistoryList(params)
    if (res.status_code === 1) {
      const list = res.data?.items || []
      if (isLoadMore) {
        historyList.value = historyList.value.concat(list)
      } else {
        historyList.value = list
      }
      // 更新分页信息
      if (res.pagination) {
        pagination.value.hasNext = res.pagination.has_next
      }
      
      // 如果不是加载更多，检查是否需要启动轮询
      if (!isLoadMore) {
        if (needContinuePolling()) {
          startPolling()
        } else {
          stopPolling()
        }
      }
    } else {
      // 获取失败重置分页
      if (isLoadMore) {
        pagination.value.page -= 1;
      }
      uni.showToast({ title: res.message || '获取失败', icon: 'none' })
    }
  } catch (e) {
    console.log(e, 'fetchError');
    if (isLoadMore) {
      pagination.value.page -= 1;
    }
    uni.showToast({ title: '网络错误', icon: 'none' })
  } finally {
    loading.value = false
  }
}

// 下拉刷新处理
const onRefresh = async () => {
  refreshing.value = true
  await fetchCreateHistroyData()
  refreshing.value = false
}

// 删除创作历史
const deleteHistory = async (taskId) => {
  uni.showModal({
    title: '提示',
    content: '确定要删除该创作历史吗？',
    success: async (res) => {
      if (res.confirm) {
        const res = await historyService.deleteCreateHistory(taskId)
        if (res.status_code === 1) {
          uni.showToast({ title: '删除成功', icon: 'success' })
          await fetchCreateHistroyData()
          scrollTop.value = 0;
        } else {
          uni.showToast({ title: res.message || '删除失败', icon: 'none' })
        }
      }
    }
  })
}

// 触底加载
const onScrollToLower = () => {
  if (pagination.value.hasNext && !loading.value) {
    fetchCreateHistroyData(true)
  }
}

const onScroll = (e) => {
  scrollTop.value = e.detail?.scrollTop
}

onMounted(() => {
  fetchCreateHistroyData()
})

onUnmounted(() => {
  stopPolling()
  // 清理下载任务
  if (currentDownloadTask.value) {
    currentDownloadTask.value.abort()
    currentDownloadTask.value = null
  }
})

// 移除原来的生命周期函数，使用 scroll-view 的内置功能

onShow(() => {
  uni.setTabBarStyle({
    color: 'rgba(140, 140, 140, 1)',
    selectedColor: '#FF0043',
    backgroundColor: '#fff',
    borderStyle: 'white'
  })
  
  // 页面显示时检查是否需要启动轮询
  if (historyList.value.length > 0 && needContinuePolling()) {
    startPolling()
  }
})

onHide(() => {
  // 页面隐藏时停止轮询以节省资源
  stopPolling()
})
</script>

<style lang="scss" scoped>
.history-header-filter-picker {
  display: flex;
  align-items: center;
}
.custom-datetime-picker {
  background: #fff;
  border-radius: 8rpx;
  border: none !important;
  height: 56rpx;
  min-width: 220rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #8A8A8A;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  box-sizing: border-box;
  padding: 0 32rpx;
  transition: all 0.2s;
  box-shadow: none !important;

  ::v-deep .uni-date-x--border {
    border: none !important;
  }

  ::v-deep .uni-date__x-input{
    height: 56rpx;
    line-height: 56rpx;
  }

  ::v-deep .range-separator {
    height: 56rpx;
    line-height: 56rpx;
  }

    
  // uni-datetime-picker 自定义样式覆盖
  ::v-deep .uni-datetime-picker {
    .uni-date-x--border {
      border: none !important;
    }

    .uni-date__x-input {
      height: 56rpx;
      line-height: 56rpx;
    }

    .range-separator {
      height: 56rpx;
      line-height: 56rpx;
    }
  }

  // uni-datetime-picker 日历弹窗样式覆盖
  ::v-deep .uni-calendar {
    // 确认按钮样式 - 这是最重要的按钮
    .uni-datetime-picker--btn {
      background-color: #FF0043 !important;
      color: #fff !important;
      border-radius: 8rpx !important;
    }
    
    // 月份文字颜色
    .uni-calendar__button-text {
      color: #FF0043 !important;
    }
  }

  // 日历项目样式需要在 calendar-item 组件中覆盖
  ::v-deep .uni-calendar-item--hook {
    // 选中日期的样式
    .uni-calendar__date--checked {
      background-color: #FF0043 !important;
      color: #fff !important;
    }
    
    // 范围选择中间日期的样式
    .uni-calendar__date--multiple {
      background-color: rgba(255, 0, 67, 0.1) !important;
    }
    
    // 今天的标记
    .uni-calendar__date--today {
      color: #FF0043 !important;
    }
    
    // hover状态
    .uni-calendar__date--hover {
      background-color: rgba(255, 0, 67, 0.1) !important;
    }
  }

  // 如果还有其他相关的日历样式类
  ::v-deep .uni-calendar__date {
    &.uni-calendar__date--checked {
      background-color: #FF0043 !important;
      color: #fff !important;
    }
    
    &.uni-calendar__date--multiple {
      background-color: rgba(255, 0, 67, 0.1) !important;
    }
    
    &.uni-calendar__date--today {
      color: #FF0043 !important;
    }
  }

  // 移动端日历样式
  ::v-deep .uni-calendar--fixed {
    .uni-calendar__date--checked {
      background-color: #FF0043 !important;
    }

    .uni-calendar__date--multiple {
      background-color: rgba(255, 0, 67, 0.1) !important;
    }

    .uni-calendar__confirm {
      background-color: #FF0043 !important;
    }
  }

  // 更强力的选中日期样式覆盖
  ::v-deep .uni-calendar__date {
    &.uni-calendar__date--checked {
      background-color: #FF0043 !important;
      color: #fff !important;
    }
    
    &.uni-calendar__date--multiple {
      background-color: rgba(255, 0, 67, 0.1) !important;
    }
    
    &.uni-calendar__date--today {
      color: #FF0043 !important;
    }
  }

  // 针对移动端弹窗的特殊处理
  ::v-deep .uni-calendar {
    .uni-calendar__date.uni-calendar__date--checked {
      background-color: #FF0043 !important;
      color: #ffffff !important;
    }
    
    .uni-calendar__date.uni-calendar__date--multiple {
      background-color: rgba(255, 0, 67, 0.15) !important;
    }
  }

  // 如果是范围选择，还需要覆盖范围样式
  ::v-deep .uni-calendar__date--range {
    background-color: rgba(255, 0, 67, 0.1) !important;
  }

  // 强制覆盖所有可能的选中状态
  ::v-deep .uni-calendar__date[class*="checked"] {
    background-color: #FF0043 !important;
    color: #fff !important;
  }

  // 精确定位选中开始日期的样式
  ::v-deep .uni-calendar-item__weeks-box-item.uni-calendar-item--before-checked {
    background-color: #FF0043 !important;
    color: #fff !important;
  }

  // 精确定位选中结束日期的样式  
  ::v-deep .uni-calendar-item__weeks-box-item.uni-calendar-item--after-checked {
    background-color: #FF0043 !important;
    color: #fff !important;
  }

  // 选中日期之间的日期样式（更浅的背景色）
  ::v-deep .uni-calendar-item__weeks-box-item.uni-calendar-item--multiple:not(.uni-calendar-item--before-checked):not(.uni-calendar-item--after-checked) {
    background-color: transparent!important;
    color: #333 !important;
  }

  // 外层容器的背景色调整
  ::v-deep .uni-calendar-item__weeks-box.uni-calendar-item--before-checked-x {
    background-color: rgba(255, 0, 67, 0.05) !important;
  }

  ::v-deep .uni-calendar-item__weeks-box.uni-calendar-item--after-checked-x {
    background-color: rgba(255, 0, 67, 0.05) !important;
  }

  // 如果还有其他范围选择的外层容器
  ::v-deep .uni-calendar-item__weeks-box.uni-calendar-item--multiple {
    background-color: rgba(255, 0, 67, 0.05) !important;
  }

  // 范围选择的整行背景
  ::v-deep .uni-calendar__weeks-item .uni-calendar-item--hook.uni-calendar-item--multiple {
    background-color: rgba(255, 0, 67, 0.05) !important;
  }

  // 选中日期的文字样式
  ::v-deep .uni-calendar-item--checked-range-text {
    color: #fff !important;
  }
}

.uni-scroll-view-refresher {
  background-color: #F9FAFB !important;
}

.status-bar {
  height: var(--status-bar-height);
  width: 100%;
}



// 顶部渐变背景
.history-header {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 10;
  background: url('@/asset/img/history/bg_top.png') no-repeat;
  background-size: 140% 140%;
  padding-top: var(--status-bar-height);
}

.history-header-bar {
  height: 88rpx;
  background: transparent;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  margin-top: 24rpx;
}
.history-header-title {
  font-size: 34rpx;
  flex: 1;
  margin-left: 64rpx;
  text-align: center;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  color: #222;
  line-height: 48rpx;
}
.history-header-manage {
  font-size: 32rpx;
  color: #262626;
  font-weight: 400;
  line-height: 44rpx;
}

// Tabs
.history-header-tabs {
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  margin-top: 24rpx;
  padding: 0 32rpx;
  gap: 48rpx;
}
.history-header-tab {
  font-size: 32rpx;
  color: #8c8c8c;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  line-height: 44rpx;
  position: relative;
  &--active {
    color: #262626;
    font-weight: 500;
    &::after {
      content: '';
      display: block;
      position: absolute;
      left: 0;
      bottom: 0;
      opacity: 0.64;
      width: 100%;
      height: 10rpx;
      background: #ff0043;
      border-radius: 3rpx;
    }
  }
}

// 筛选区
.history-header-filters {
  background: transparent;
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  margin-top: 24rpx;
  margin-bottom: 32rpx;
  padding-left: 32rpx;
  gap: 24rpx;
}
.history-header-filter {
  background: #fff;
  border-radius: 8rpx;
  padding: 6rpx 22rpx 6rpx 30rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color:#595959;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  &--active {
    color: #ff0043;
  }

  .history-header-filter-icon {
    width: 20rpx;
    height: 12rpx;
    margin-left: 8rpx;
  }
}

.history-header-filter-wrapper {
  position: relative;
  display: inline-block;
}

// 滚动区域样式
.history-scroll {
  position: absolute;
  top: calc(280rpx + var(--status-bar-height)); /* 头部实际高度 */
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  background: #f9f9f9;
  min-height: 200rpx; /* 确保有最小高度 */
  -webkit-overflow-scrolling: touch; /* iOS 滚动优化 */
}

.history-content {
  padding: 24rpx;
  padding-bottom: 48rpx;
  min-height: 120vh; /* 确保内容高度足够触发滚动 */
  padding-top: 0;
}

// 历史分组
.history-list {
  padding: 0;
}
.history-group {
  margin-bottom: 36rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 24rpx;
  .history-group__date {
    font-size: 30rpx;
    color: #262626;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    margin-bottom: 20rpx;
    display: block;
    text-align: left;
  }
  // 主卡片区
  .history-group__maincard {
    background: #fff;
    border-radius: 20rpx;
    padding: 20rpx;
    margin-bottom: 24rpx;
    border-radius: 16rpx;
    border: 2rpx solid #EDEDEF;
    .history-group__maincard-header {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 18rpx;
    }
    .history-group__maincard-title {
      font-size: 28rpx;
      color: #262626;
      font-weight: 500;
      line-height: 40rpx;
      white-space: nowrap;
    }
    .history-group__maincard-time {
      font-size: 24rpx;
      color: #a8a8a8;
      line-height: 34rpx;
      white-space: nowrap;
    }
    .history-group__maincard-content {
      display: flex;
      align-items: flex-start;
      .history-group__maincard-img-box {
        position: relative;
        border-radius: 16rpx;
        width: 172rpx;
        height: 172rpx;
        overflow: hidden;
        .history-group__maincard-video {
          width: 100%;
          height: 100%;
        }
        .history-group__maincard-img {
          width: 100%;
          height: 100%;
        }
        .history-group__maincard-play-icon {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 48rpx;
          height: 48rpx;
        }
      }
      .history-group__maincard-desc {
        margin-left: 20rpx;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 172rpx; // 与图片高度一致
        
        .history-group__maincard-desc-text {
          font-size: 26rpx;
          color: #8c8c8c;
          line-height: 36rpx;
          width: 420rpx;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          height: 72rpx;
        }
        .history-group__maincard-status--progress {
          margin-left: 0;
          margin-top: 8rpx;
          width: 100%;
          
          .history-group__maincard-progress-container {
            display: flex;
            align-items: center;
            width: 100%;
            gap: 16rpx;
            
            .history-group__maincard-progress-bar {
              flex: 1;
              height: 32rpx;
              background-color: #f0f0f0;
              border-radius: 16rpx;
              overflow: hidden;
              position: relative;
              box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.08);
              border: 1rpx solid rgba(0, 0, 0, 0.05);
              
              .history-group__maincard-progress-fill {
                height: 100%;
                background: linear-gradient(135deg, #FF0043 0%, #ff2d55 50%, #ff6b6b 100%);
                border-radius: 16rpx;
                transition: width 0.3s ease;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: inset 0 2rpx 4rpx rgba(255, 255, 255, 0.4);
                min-width: 140rpx;
                
                &::before {
                  content: '';
                  position: absolute;
                  top: 0;
                  left: 0;
                  right: 0;
                  height: 50%;
                  background: linear-gradient(180deg, rgba(255, 255, 255, 0.25) 0%, transparent 100%);
                  border-radius: 16rpx 16rpx 0 0;
                }
                
                &::after {
                  content: '';
                  position: absolute;
                  top: 2rpx;
                  left: 2rpx;
                  right: 2rpx;
                  height: 2rpx;
                  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.5) 50%, transparent 100%);
                  border-radius: 1rpx;
                }
                
                .history-group__maincard-progress-text {
                  font-size: 18rpx;
                  color: #ffffff;
                  line-height: 24rpx;
                  font-weight: 600;
                  white-space: nowrap;
                  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
                  z-index: 1;
                  position: relative;
                  letter-spacing: 0.5rpx;
                  min-width: 100rpx;
                  text-align: center;
                  font-family: PingFangSC-Medium;
                }
              }

              .history-group__maincard-progress-text--no-fill {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 2;
                height: 100%;
                
                text {
                  font-size: 16rpx;
                  color: #000;
                  line-height: 24rpx;
                  font-weight: 500;
                  white-space: nowrap;
                  letter-spacing: 0.5rpx;
                  font-family: PingFangSC-Regular;
                }
              }
            }
            
            .history-group__maincard-btn--delete-progress {
              width: 48rpx;
              height: 48rpx;
              background: #F7F7F7;
              border-radius: 28rpx;
              flex-shrink: 0;

              .history-group__maincard-delete-img {
                width: 100%;
                height: 100%;
              }
            }
          }
        }
        .history-group__maincard-status--completed {
          margin-top: 56rpx;
          display: flex;
          flex-direction: row;
          justify-content: space-evenly;

          .history-group__maincard-btn--delete {
            width: 48rpx;
            height: 48rpx;
            background: #F7F7F7;
            border-radius: 28rpx;

            .history-group__maincard-delete-img {
              width: 100%;
              height: 100%;
            }
          }
          .history-group__maincard-btn {
            width: 108rpx;
            height: 48rpx;
            background: #F7F7F7;
            border-radius: 28rpx;
            font-weight: 400;
            font-size: 24rpx;
            color: #121215;
            text-align: center;
            line-height: 48rpx;
          }
          .history-group__maincard-btn--publish {
            color: #fff;
            background-color: #FF0043;
          }
        }
      }
    }
  }
  // 卡片区
  .history-group__items {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
  }
  .history-group__item {
    width: 204rpx;
    min-height: 274rpx;
    background: #fff;
    border-radius: 14rpx;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2rpx 8rpx 0 rgba(0,0,0,0.04);
    &-img {
      position: absolute;
      inset: 0;
      width: 100%;
      height: 100%;
      border-radius: 8rpx;
      object-fit: cover;
    }
    &-status--generating {
      position: absolute;
      left: 0;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      text-align: center;
      border-radius: 0 0 0 14rpx;
      font-size: 24rpx;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      color: #fff;
      padding: 0 32rpx;
      .history-group__item-status-img {
        display: block;
        width: 42rpx;
        height: 42rpx;
        margin: auto;
        margin-bottom: 8rpx;
      }
      .history-group__item-status-time {
        font-size: 20rpx;
      }
    }
    &-mask {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 44rpx;
    }
    &-play-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 44rpx;
      height: 44rpx;
    }
    &-status--wait {
      position: absolute;
      inset: 0;
      .history-group__item-status-tag {
        position: absolute;
        right: 0;
        top: 0;
        width: 78rpx;
        height: 32rpx;
        background: #FF0043;
        border-radius: 0 14rpx 0 17rpx;
        padding: 4rpx 8rpx 4rpx 10rpx;
        font-weight: 400;
        font-size: 20rpx;
        color: #FFFFFF;
        line-height: 24rpx;
      }
      .history-group__item-status-count {
        position: absolute;
        font-size: 20rpx;
        margin-left: 8rpx;
        color: #fff;
        bottom: 8rpx;
        right: 12rpx;
        z-index: 2;
      }
    }
    &-duration {
      position: absolute;
      bottom: 8rpx;
      right: 12rpx;
      color: #fff;
      font-size: 20rpx;
      z-index: 2;
      font-weight: 500;
    }
  }
}

.history-nomore {
  font-weight: 400;
  font-size: 28rpx;
  color: #BFBFBF;
  line-height: 40rpx;
  text-align: center;
  margin-top: 40rpx;
}

// 浮动创作按钮
.history-create-btn-fixed {
  position: fixed;
  right: 48rpx;
  bottom: 120rpx;
  z-index: 9;
  background: linear-gradient(90deg, #ff2d55 0%, #ff6b6b 100%);
  border-radius: 44rpx;
  width: 180rpx;
  height: 80rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 2rpx 7rpx 0px rgba(0, 0, 0, 0.11);
  .history-create-btn-fixed__img {
    width: 32rpx;
    height: 32rpx;
    margin-right: 12rpx;
  }
  .history-create-btn-fixed__text {
    color: #fff;
    font-size: 32rpx;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    line-height: 44rpx;
  }
}
.history-publish-modal__mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.5);
  z-index: 101;
}
.history-publish-modal__container {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 38rpx 40rpx 22rpx 30rpx;
  z-index: 110;
  box-sizing: border-box;
  animation: modalUp 0.2s;
}
@keyframes modalUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}
.history-publish-modal__title {
  color: #262626;
  font-size: 24rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 34rpx;
  margin-bottom: 40rpx;
}
.history-publish-modal__grid {
  width: 552rpx;
  display: flex;
  flex-wrap: wrap;
  margin: 0 40rpx 40rpx 0;
}
.history-publish-modal__item {
  width: 108rpx;
  height: 148rpx;
  margin: 0 40rpx 40rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}
.history-publish-modal__item:nth-child(4n) {
  margin-right: 0;
}
.history-publish-modal__item:nth-last-child(-n + 4) {
  margin-bottom: 0;
}
.history-publish-modal__icon {
  width: 108rpx;
  height: 108rpx;
}
.history-publish-modal__text {
  color: #595959;
  font-size: 20rpx;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin-top: 12rpx;
}
.history-publish-modal__divider {
  width: 670rpx;
  height: 2rpx;
  background: #f2f2f2;
}
.history-publish-modal__cancel {
  color: #262626;
  font-size: 32rpx;
  text-align: center;
  line-height: 44rpx;
  padding: 20rpx 302rpx 0 314rpx;
  font-weight: normal;
  cursor: pointer;
}

.fail-text {
  font-size: 24rpx;
  color: #ff0043;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  line-height: 48rpx;
  height: 48rpx;
  white-space: nowrap;
  align-self: center;
  display: flex;
  align-items: center;
}
.status-dropdown-mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.01);
  z-index: 100;
}
.status-dropdown {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 100%;
  margin-top: 8rpx;
  min-width: 260rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(0,0,0,0.08);
  z-index: 101;
  padding: 12rpx 0;
}
.status-dropdown-item {
  padding: 18rpx 40rpx;
  font-size: 28rpx;
  color: #262626;
  cursor: pointer;
}
.status-dropdown-item--active {
  color: #ff0043;
  font-weight: 600;
  background: #f7f7f7;
}
</style>
