<template>
  <view class="team-page">
    <!-- 固定头部区域 -->
    <view class="team-header-wrap">
      <view class="team-header-title">
        <view class="team-navbar-left" @click="handleBack">
          <image class="team-navbar-back" src="@/asset/img/team/icon_back.png" />
        </view>
        <text class="team-navbar-title">团队成员</text>
        <view class="team-navbar-right" @click="handleAllocatePower">
          <text class="team-navbar-allocate">分配算力</text>
        </view>
      </view>
    </view>

    <!-- 可滚动内容区域 -->
    <scroll-view class="team-scroll" scroll-y="true">
      <view class="team-content">
        <!-- 加载状态 -->
        <view v-if="loading && teamMembers.length === 0" class="team-loading">
          <text class="team-loading-text">加载中...</text>
        </view>
        
        <!-- 空状态 -->
        <view v-else-if="!loading && teamMembers.length === 0" class="team-empty">
          <text class="team-empty-text">暂无团队成员</text>
        </view>
        
        <!-- 团队成员列表 -->
        <view v-else>
          <!-- 创建者区域 -->
          <view v-if="teamMembers.filter(m => m.user_type === 1).length > 0" class="team-section">
            <view class="team-section-header">
              <text class="team-section-title">创建者</text>
            </view>
            <view class="team-members-card">
              <view 
                v-for="member in teamMembers.filter(m => m.user_type === 1)" 
                :key="member.id"
                class="team-member-item" 
                @click="handleCreatorClick"
              >
                <view class="team-member-avatar">
                  <image 
                    class="team-member-avatar-img" 
                    :src="member.avatar || profileAvatar" 
                    mode="aspectFill"
                  />
                </view>
                <view class="team-member-info">
                  <text class="team-member-name">{{ member.nickname || member.username }}</text>
                  <text class="team-member-role">创建者</text>
                </view>
                <view class="team-member-status">
                  <text class="team-member-status-text">不可更改</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 成员区域 -->
          <view class="team-section">
            <view class="team-section-header">
              <text class="team-section-title">成员</text>
            </view>
            <!-- 有成员时显示成员列表 -->
            <view v-if="teamMembers.filter(m => m.user_type === 2).length > 0" class="team-members-card">
              <view 
                v-for="member in teamMembers.filter(m => m.user_type === 2)" 
                :key="member.id"
                class="team-member-item" 
                @click="handleMemberClick(member)"
              >
                <view class="team-member-avatar">
                  <image 
                    class="team-member-avatar-img" 
                    :src="member.avatar || profileAvatar" 
                    mode="aspectFill"
                  />
                </view>
                <view class="team-member-info">
                  <view class="team-member-header">
                    <!-- 权限角色标签 -->
                    <view v-if="member.roles && member.roles.length > 0" class="team-member-role-tags">
                      <text 
                        v-for="role in member.roles" 
                        :key="role.id"
                        class="team-member-role-tag"
                      >
                        {{ role.name }}
                      </text>
                    </view>
                    <text class="team-member-name">{{ member.nickname || member.username }}</text>
                  </view>
                  <!-- 角色描述 -->
                  <view v-if="member.roles && member.roles.length > 0" class="team-member-description">
                    <text 
                      v-for="role in member.roles" 
                      :key="role.id"
                      class="team-member-description-text"
                    >
                      {{ role.description }}
                    </text>
                  </view>
                </view>
                <view class="team-member-arrow-container">
                  <image class="team-member-arrow" src="@/asset/img/profile/profile-menu-arrow.png" />
                </view>
              </view>
            </view>
            <!-- 无成员时显示提示 -->
            <view v-else class="team-empty-members">
              <text class="team-empty-members-text">暂无成员</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 权限修改弹窗 -->
    <view v-if="showPermissionModal" class="team-permission-modal">
      <view class="team-permission-overlay" @click="!isUpdatingPermission && closePermissionModal()"></view>
      <view class="team-permission-content">
        <!-- 加载遮罩层 -->
        <view v-if="isUpdatingPermission" class="team-permission-loading-overlay">
          <view class="team-permission-loading-content">
            <text class="team-permission-loading-text">正在修改权限...</text>
          </view>
        </view>
        
        <!-- 弹窗头部 -->
        <view class="team-permission-header">
          <view class="team-permission-close" :class="{ 'team-permission-close--disabled': isUpdatingPermission }" @click="!isUpdatingPermission && closePermissionModal()">
            <image class="team-permission-close-icon" src="@/asset/img/team/icon_close.png" />
          </view>
          <text class="team-permission-title">修改成员权限</text>
          <view class="team-permission-confirm" :class="{ 'team-permission-confirm--disabled': isUpdatingPermission }" @click="handleConfirmPermission">
            <text class="team-permission-confirm-text">确认</text>
          </view>
        </view>

        <!-- 权限选项 -->
        <view class="team-permission-options">
          <view 
            v-for="role in rolesList" 
            :key="role.id"
            class="team-permission-option" 
            :class="{ 'team-permission-option--active': selectedPermission === role.id }"
            @click="selectPermission(role.id)"
          >
            <view class="team-permission-option-content">
              <text class="team-permission-option-title">{{ role.name }}</text>
              <text class="team-permission-option-desc">{{ role.description }}</text>
              <!-- 权限资源列表 -->
              <view v-if="role.resources && role.resources.length > 0" class="team-permission-resources">
                <text class="team-permission-resources-title">包含权限：</text>
                <view class="team-permission-resources-list">
                  <text 
                    v-for="resource in role.resources" 
                    :key="resource.id"
                    class="team-permission-resource-item"
                  >
                    {{ resource.name }}
                  </text>
                </view>
              </view>
            </view>
            <image 
              v-if="selectedPermission === role.id" 
              class="team-permission-option-check" 
              src="@/asset/img/team/icon_check_white.png" 
            />
          </view>
        </view>

        <!-- 移除成员按钮 -->
        <view class="team-permission-remove" @click="handleRemoveMember">
          <text class="team-permission-remove-text">移除成员</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { permissionService, userService } from '@/service'
import { getCid } from '@/utils/http'

// 响应式数据
const showPermissionModal = ref(false)
const selectedPermission = ref(null)
const rolesList = ref([]) // 角色权限列表
const isUpdatingPermission = ref(false) // 权限修改加载状态

// 导入头像图片
import profileAvatar from '@/asset/img/profile/profile-header-avatar.png'

// 团队成员数据
const teamMembers = ref([])

// 分页信息
const pagination = ref({
  page: 1,
  size: 10,
  total: 0
})

// 加载状态
const loading = ref(false)

// 当前选中的成员
const selectedMember = ref(null)

// 返回上一页
const handleBack = () => {
  uni.navigateBack()
}

// 分配算力
const handleAllocatePower = () => {
  uni.navigateTo({
    url: '/pages/team/allocate/index'
  })
}

// 点击创建者
const handleCreatorClick = () => {
  uni.showToast({
    title: '创建者权限不可更改',
    icon: 'none'
  })
}

// 点击成员
const handleMemberClick = (member) => {
  // 只有成员（user_type为2）才能修改权限
  if (member.user_type !== 2) {
    uni.showToast({
      title: '创建者权限不可更改',
      icon: 'none'
    })
    return
  }
  
  selectedMember.value = member
  // 根据成员的权限找到对应的角色ID，如果没有找到则默认选择第一个角色
  const permission = member.roles && member.roles.length > 0 ? member.roles[0].name : '成员'
  const roleId = rolesList.value.find(role => role.name === permission)?.id || rolesList.value[0]?.id
  selectedPermission.value = roleId
  showPermissionModal.value = true
}

// 关闭权限弹窗
const closePermissionModal = () => {
  // 如果正在更新权限，不允许关闭弹窗
  if (isUpdatingPermission.value) {
    return
  }
  
  showPermissionModal.value = false
  // 清除选中的成员
  selectedMember.value = null
}

// 选择权限
const selectPermission = (permission) => {
  selectedPermission.value = permission
}

// 确认权限修改
const handleConfirmPermission = async () => {
  if (!selectedMember.value || !selectedPermission.value) {
    uni.showToast({
      title: '请选择权限',
      icon: 'none'
    })
    return
  }

  // 防止重复操作
  if (isUpdatingPermission.value) {
    return
  }

  try {
    isUpdatingPermission.value = true
    
    const selectedRole = rolesList.value.find(role => role.id === selectedPermission.value)
    if (!selectedRole) {
      uni.showToast({
        title: '角色不存在',
        icon: 'none'
      })
      return
    }

    // 获取当前用户的CID
    const cid = getCid()
    if (!cid) {
      uni.showToast({
        title: '获取公司信息失败',
        icon: 'none'
      })
      return
    }

    // 先分配新角色
    const assignData = {
      user_id: selectedMember.value.id,
      role_ids: [selectedRole.id],
      target_cid: cid
    }

    const response = await permissionService.assignUserRoles(assignData)
    
    if (response.status_code === 1) {
      // 分配成功后，删除用户原有的角色
      if (selectedMember.value.roles && selectedMember.value.roles.length > 0) {
        for (const role of selectedMember.value.roles) {
          try {
            await permissionService.deleteUserRole(selectedMember.value.id, role.id, cid)
          } catch (error) {
            console.error('删除用户角色失败:', error)
          }
        }
      }
      
      uni.showToast({
        title: '权限修改成功',
        icon: 'success'
      })
      
      // 刷新团队成员列表，确保显示最新的权限资源标签
      await fetchTeamMembers()
    } else {
      uni.showToast({
        title: response.message || '权限修改失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('权限修改失败:', error)
    uni.showToast({
      title: '权限修改失败，请重试',
      icon: 'none'
    })
  } finally {
    isUpdatingPermission.value = false
    closePermissionModal()
  }
}

// 移除成员
const handleRemoveMember = () => {
  // 先关闭权限修改弹窗，避免层级冲突
  closePermissionModal()
  
  // 延迟显示确认弹窗，确保权限弹窗完全关闭
  setTimeout(() => {
    uni.showModal({
      title: '确认移除',
      content: '确定要移除该成员吗？',
      success: (res) => {
        if (res.confirm && selectedMember.value) {
          // 检查是否为创建者
          if (selectedMember.value.user_type === 1) {
            uni.showToast({
              title: '创建者不能被移除',
              icon: 'none'
            })
            return
          }
          
          // 从列表中移除成员
          const memberIndex = teamMembers.value.findIndex(m => m.id === selectedMember.value.id)
          if (memberIndex !== -1) {
            teamMembers.value.splice(memberIndex, 1)
            // 清除选中的成员
            selectedMember.value = null
          }
          
          uni.showToast({
            title: '成员已移除',
            icon: 'success'
          })
        }
      }
    })
  }, 150)
}

// 获取团队成员列表
const fetchTeamMembers = async (page = 1, size = 10) => {
  try {
    loading.value = true
    const params = {
      page,
      size
    }
    
    const response = await userService.getTeamMembers(params)
    console.log('团队成员列表:', response)
    
    // 检查响应状态码
    if (response.status_code !== 1) {
      // 显示错误信息
      const errorMessage = response.message || '获取团队成员失败'
      uni.showToast({
        title: errorMessage,
        icon: 'none'
      })
      return
    }
    
    // 检查data是否存在且有items
    if (response.data && response.data.items) {
      // 处理成员数据，结合权限角色信息
      const processedMembers = response.data.items.map(member => {
        // 如果成员有角色信息，需要结合权限角色数据
        if (member.roles && member.roles.length > 0) {
          // 为每个成员的角色添加完整的权限资源信息
          const processedRoles = member.roles.map(role => {
            // 从权限角色列表中查找对应的完整角色信息
            const fullRole = rolesList.value.find(r => r.id === role.id || r.name === role.name)
            if (fullRole) {
              return {
                ...role,
                resources: fullRole.resources || []
              }
            }
            return role
          })
          
          return {
            ...member,
            roles: processedRoles
          }
        }
        return member
      })
      
      // 直接使用处理后的数据
      if (page === 1) {
        teamMembers.value = processedMembers
      } else {
        teamMembers.value = [...teamMembers.value, ...processedMembers]
      }
      
      // 更新分页信息
      if (response.pagination) {
        pagination.value = {
          page: response.pagination.page || page,
          size: response.pagination.size || size,
          total: response.pagination.total || 0
        }
      }
    } else {
      // 如果没有数据，清空列表
      teamMembers.value = []
      pagination.value = {
        page: 1,
        size: 10,
        total: 0
      }
    }
  } catch (error) {
    console.error('获取团队成员列表失败:', error)
    uni.showToast({
      title: '网络请求失败，请重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 获取角色权限列表
const fetchRoles = async () => {
  try {
    const response = await permissionService.getRoles()
    console.log('角色权限列表:', response)
    
    // 检查响应状态码
    if (response.status_code !== 1) {
      // 显示错误信息
      const errorMessage = response.message || '获取权限列表失败'
      uni.showToast({
        title: errorMessage,
        icon: 'none'
      })
      return
    }
    
    // 检查data是否存在
    if (response.status_code === 1 && response.data) {
      rolesList.value = response.data
    } else {
      rolesList.value = []
    }
  } catch (error) {
    console.error('获取角色权限列表失败:', error)
    uni.showToast({
      title: '获取权限列表失败',
      icon: 'error'
    })
  }
}

// 页面加载时调用接口
onMounted(async () => {
  // 先获取权限角色列表
  await fetchRoles()
  // 再获取团队成员列表
  await fetchTeamMembers()
})
</script>

<style lang="scss" scoped>
.team-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #ffdede 0%, #f5f5f5 30%, #f9f9f9 60%, #ffffff 100%);
  position: relative;
}

// 固定头部区域
.team-header-wrap {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  height: calc(88rpx + var(--status-bar-height));
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #ffdede;
  box-shadow: none;
}

.team-header-title {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 24rpx;
  padding-top: var(--status-bar-height);
  box-sizing: border-box;
}

.team-navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.team-navbar-back {
  width: 40rpx;
  height: 40rpx;
}

.team-navbar-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #000;
  flex: 1;
  text-align: center;
}

.team-navbar-right {
  width: 120rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.team-navbar-allocate {
  font-size: 28rpx;
  color: #666;
  font-weight: 400;
}

// 可滚动内容区域
.team-scroll {
  margin-top: calc(88rpx + var(--status-bar-height));
  padding: 24rpx;
  height: calc(100vh - 88rpx - var(--status-bar-height));
  background: transparent;
}

.team-content {
  background-color: transparent;
}

// 成员区域
.team-section {
  margin-bottom: 48rpx;
}

.team-section-header {
  margin-bottom: 24rpx;
  position: relative;
  display: flex;
  align-items: center;
}

.team-section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}

// 加载状态
.team-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx 0 rgba(0,0,0,0.06);
}

.team-loading-text {
  font-size: 28rpx;
  color: #666;
}

// 空状态
.team-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx 0 rgba(0,0,0,0.06);
}

.team-empty-text {
  font-size: 28rpx;
  color: #999;
}

// 成员区域空状态
.team-empty-members {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx 0 rgba(0,0,0,0.06);
}

.team-empty-members-text {
  font-size: 26rpx;
  color: #999;
}

// 团队成员卡片
.team-members-card {
  border-radius: 20rpx;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 8rpx 32rpx 0 rgba(0,0,0,0.06);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

// 成员项
.team-member-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
}

.team-member-item:last-child {
  border-bottom: none;
}

.team-member-item:active {
  background-color: #f8f8f8;
  transform: scale(0.98);
}

.team-member-avatar {
  margin-right: 24rpx;
  flex-shrink: 0;
  position: relative;
}

.team-member-avatar-img {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
  border: 3rpx solid #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.team-member-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.team-member-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  min-width: 0;
}

.team-member-role-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.team-member-role-tag {
  font-size: 22rpx;
  color: #B8860B;
  background: linear-gradient(135deg, rgba(184, 134, 11, 0.1) 0%, rgba(184, 134, 11, 0.2) 100%);
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  border: 1rpx solid rgba(184, 134, 11, 0.2);
  font-weight: 500;
}

.team-member-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
}

.team-member-role {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
  font-weight: 500;
}

.team-member-description {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: 8rpx;
}

.team-member-description-text {
  font-size: 26rpx;
  color: #999;
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  line-height: 1.4;
}

.team-member-status {
  display: flex;
  align-items: center;
  margin-top: 8rpx;
}

.team-member-status-text {
  font-size: 24rpx;
  color: #4CAF50;
  font-weight: 500;
  
  &--disabled {
    color: #ff4757;
  }
}

.team-member-arrow-container {
  width: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.team-member-arrow {
  width: 14rpx;
  height: 24rpx;
  object-fit: contain;
  opacity: 0.6;
}

// 权限修改弹窗
.team-permission-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.team-permission-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4rpx);
}

.team-permission-content {
  position: relative;
  width: 100%;
  background: linear-gradient(180deg, #fff 0%, #f8f8f8 100%);
  border-radius: 32rpx 32rpx 0 0;
  padding: 32rpx;
  max-height: 80vh;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
}

// 加载遮罩层
.team-permission-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 101;
  backdrop-filter: blur(4rpx);
}

.team-permission-loading-content {
  background: #fff;
  padding: 40rpx;
  border-radius: 20rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.team-permission-loading-text {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

// 弹窗头部
.team-permission-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.team-permission-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.team-permission-close:active {
  background: #e0e0e0;
  transform: scale(0.95);
}

.team-permission-close-icon {
  width: 44rpx;
  height: 44rpx;
}

.team-permission-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  text-align: center;
}

.team-permission-confirm {
  width: 120rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
}

.team-permission-confirm:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
}

.team-permission-confirm-text {
  font-size: 28rpx;
  color: #fff;
  font-weight: 600;
}

.team-permission-confirm--disabled {
  background: linear-gradient(135deg, #ccc 0%, #999 100%);
  box-shadow: none;
  cursor: not-allowed;
}

.team-permission-close--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

// 权限选项
.team-permission-options {
  margin-bottom: 32rpx;
}

.team-permission-option {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 28rpx;
  margin-bottom: 16rpx;
  border-radius: 20rpx;
  background: #f9f9f9;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

  &--active {
    background: linear-gradient(135deg, #333 0%, #666 100%);
    border-color: #4CAF50;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
    transform: translateY(-2rpx);
  }
}

.team-permission-option:active {
  transform: scale(0.98);
}

.team-permission-option-content {
  flex: 1;
}

.team-permission-option-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;

  .team-permission-option--active & {
    color: #fff;
  }
}

.team-permission-option-desc {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 12rpx;
  line-height: 1.4;

  .team-permission-option--active & {
    color: #ccc;
  }
}

// 权限资源列表样式
.team-permission-resources {
  margin-top: 12rpx;
}

.team-permission-resources-title {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 8rpx;
  font-weight: 500;

  .team-permission-option--active & {
    color: #aaa;
  }
}

.team-permission-resources-list {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8rpx;
}

.team-permission-resource-item {
  font-size: 22rpx;
  color: #666;
  background: #f0f0f0;
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  display: inline-block;
  border: 1rpx solid #e0e0e0;

  .team-permission-option--active & {
    color: #ccc;
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
  }
}

.team-permission-option-check {
  width: 44rpx;
  height: 44rpx;
  filter: brightness(0) invert(1);
}

// 移除成员按钮
.team-permission-remove {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 28rpx;
  background: #fff;
  border-radius: 20rpx;
  border: 2rpx solid #ff4757;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.1);
}

.team-permission-remove:active {
  background: #fff5f5;
  transform: scale(0.98);
}

.team-permission-remove-text {
  font-size: 30rpx;
  color: #ff4757;
  font-weight: 600;
}

// 成员权限角色标签
.team-member-resources {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: 8rpx;
}

.team-member-resource-tag {
  font-size: 22rpx;
  color: #B8860B;
  background: linear-gradient(135deg, rgba(184, 134, 11, 0.1) 0%, rgba(184, 134, 11, 0.2) 100%);
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  border: 1rpx solid rgba(184, 134, 11, 0.2);
  font-weight: 500;
}
</style>