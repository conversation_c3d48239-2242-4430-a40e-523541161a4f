/**
 * 团队相关接口服务
 */

import http from '@/utils/http'

const teamService = {
  /**
   * 获取团队算力信息
   * @param {Object} params - 请求参数
   * @param {number} params.cid - 公司ID
   * @param {number} params.user_id - 用户ID
   * @returns {Promise}
   */
  getTeamComputePower(params = {}) {
    return http.get('/api/v1/compute-power/account/team', params)
  },

  /**
   * 分配团队算力
   * @param {Object} data - 分配数据
   * @param {number} data.to_user_id - 目标用户ID
   * @param {number} data.amount - 分配的算力数量
   * @param {string} data.remark - 备注信息
   * @returns {Promise}
   */
  allocateComputePower(data) {
    return http.post('/api/v1/compute-power/allocate', data)
  }
}

export default teamService
