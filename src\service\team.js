/**
 * 团队相关接口服务
 */

import http from '@/utils/http'

const teamService = {
  /**
   * 获取团队算力信息
   * @param {Object} params - 请求参数
   * @param {number} params.cid - 公司ID
   * @param {number} params.user_id - 用户ID
   * @returns {Promise}
   */
  getTeamComputePower(params = {}) {
    return http.get('/api/v1/compute-power/account/team', params)
  },

  /**
   * 获取团队成员列表
   * @param {Object} params - 请求参数
   * @returns {Promise}
   */
  getTeamMembers(params = {}) {
    return http.get('/api/v1/auth/users', params)
  },

  /**
   * 分配算力给团队成员
   * @param {Object} data - 分配数据
   * @param {number} data.memberId - 成员ID
   * @param {number} data.power - 分配的算力数量
   * @returns {Promise}
   */
  allocatePower(data) {
    return http.post('/api/v1/compute-power/allocate', data)
  },

  /**
   * 获取算力分配明细
   * @param {Object} params - 请求参数
   * @returns {Promise}
   */
  getPowerAllocationDetails(params = {}) {
    return http.get('/api/v1/compute-power/allocation-details', params)
  },

  /**
   * 获取算力消耗明细
   * @param {Object} params - 请求参数
   * @returns {Promise}
   */
  getPowerConsumptionDetails(params = {}) {
    return http.get('/api/v1/compute-power/consumption-details', params)
  },

  /**
   * 获取团队算力统计
   * @param {Object} params - 请求参数
   * @returns {Promise}
   */
  getTeamPowerStats(params = {}) {
    return http.get('/api/v1/compute-power/team-stats', params)
  }
}

export default teamService
